"use server";

import { auth } from "@/lib/auth";
import { ActionResult, LoginSchema } from "@/lib/schema";
import { APIError } from "better-auth/api";


export async function loginUser({
  email,
  password,
}: {
  email: string;
  password: string;
}): Promise<ActionResult<{ user: { id: string; email: string } }>> {
  try {

    const parsed = LoginSchema.safeParse({ email, password });
    if (!parsed.success) {
      return {
        error: { reason: parsed.error.flatten().formErrors[0] || "Invalid email or password" },
        success: null,
      };
    }

    await auth.api.signInEmail({ body: {
      email: parsed.data.email,
      password: parsed.data.password,
  }});

    return {
      success: { reason: "Login successful" },
      error: null,
      data: undefined,
    };
  } catch (err) {
    if (err instanceof APIError) {
      return {
        error: { reason: err.message },
        success: null,
      };
    }

    return { error: { reason: "Something went wrong." }, success: null };
  }
}
