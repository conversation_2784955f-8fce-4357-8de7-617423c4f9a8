/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { ActionResult, LoginSchema } from "@/lib/schema";
import { APIError } from "better-auth/api";

// Define user roles
export type UserRole = "ADMIN" | "CUSTOMER";

// Extended ActionResult to include user data with role
export type LoginActionResult = ActionResult<{
  user: {
    id: string;
    email: string;
    role: UserRole;
  }
}>;

export async function loginUser({
  email,
  password,
}: {
  email: string;
  password: string;
}): Promise<LoginActionResult> {
  try {
    const parsed = LoginSchema.safeParse({ email, password });
    if (!parsed.success) {
      return {
        error: { reason: parsed.error.flatten().formErrors[0] || "Invalid email or password" },
        success: null,
      };
    }

    // Sign in the user
    const signInResult = await auth.api.signInEmail({
      body: {
        email: parsed.data.email,
        password: parsed.data.password,
      }
    });

    // The signInResult already contains user data
    if (!signInResult.user) {
      return {
        error: { reason: "Failed to get user data" },
        success: null,
      };
    }

    // Option 1: If role is already in the user object
    let userRole = (signInResult.user as any).role as UserRole;

    // Option 2: If you need to fetch role from database
    if (!userRole) {
      const userWithRole = await prisma.user.findUnique({
        where: { id: signInResult.user.id },
        select: { role: true }
      });
      userRole = userWithRole?.role as UserRole;
    }

    // Update lastLoginAt for the user
    try {
      await prisma.user.update({
        where: { id: signInResult.user.id },
        data: { lastLoginAt: new Date() },
      });
      console.log(`Updated lastLoginAt for user: ${signInResult.user.email}`);
    } catch (error) {
      console.error("Failed to update lastLoginAt:", error);
      // Don't fail the login if lastLoginAt update fails
    }

    return {
      success: { reason: "Login successful" },
      error: null,
      data: {
        user: {
          id: signInResult.user.id,
          email: signInResult.user.email,
          role: userRole
        }
      }
    };
  } catch (err) {
    if (err instanceof APIError) {
      return {
        error: { reason: err.message },
        success: null,
      };
    }

    return { error: { reason: "Something went wrong." }, success: null };
  }
}