"use server";

import { getSession } from "@/data/get-session";
import { prisma } from "@/lib/prisma";

export async function updateLastLogin() {
  try {
    const session = await getSession();
    
    if (!session || !session.user) {
      throw new Error("No active session found");
    }

    await prisma.user.update({
      where: { id: session.user.id },
      data: { lastLoginAt: new Date() },
    });

    console.log(`Updated lastLoginAt for user: ${session.user.email}`);
    return { success: true };
  } catch (error) {
    console.error("Failed to update lastLoginAt:", error);
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}
