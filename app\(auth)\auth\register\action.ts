"use server";

import { auth } from "@/lib/auth";
import { APIError } from "better-auth/api";
import { ActionResult, RegisterFormData, RegisterSchema } from "@/lib/schema";

export async function registerUser(
  formData: RegisterFormData,
): Promise<ActionResult> {
  const parsed = RegisterSchema.safeParse(formData);

  if (!parsed.success) {
    return {
      success: null,
      error: { reason: parsed.error.flatten().formErrors[0] || "Invalid input" },
    };
  }

  const { email, password, name } = parsed.data;

  try {
    await auth.api.signUpEmail({
      body: {
        email,
        password,
        name,
      },
    });

    return {
      success: {
        reason:
          "Registration successful!",
          
      },
      error: null,
      data: null,
    };
  } catch (error) {
    if (error instanceof APIError) {
      switch (error.status) {
        case "UNPROCESSABLE_ENTITY":
          return { error: { reason: "User already exists." }, success: null };
        case "BAD_REQUEST":
          return { error: { reason: "Invalid email." }, success: null };
        default:
          return { error: { reason: "Something went wrong." }, success: null };
      }
    }

    return { error: { reason: "Something went wrong." }, success: null };
  }
}
