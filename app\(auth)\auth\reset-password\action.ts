"use server";

import { auth } from "@/lib/auth";
import { ActionResult, ResetPasswordFormData, ResetPasswordSchema } from "@/lib/schema";
import { APIError } from "better-auth/api";

export async function resetPassword(
  formData: ResetPasswordFormData & { token: string },
): Promise<ActionResult> {
  const parsed = ResetPasswordSchema.safeParse({
    password: formData.password,
    confirmPassword: formData.confirmPassword,
  });

  if (!parsed.success) {
    const errors = parsed.error.flatten().fieldErrors;
    const firstError = errors.password?.[0] || errors.confirmPassword?.[0] || "Invalid input";
    return {
      success: null,
      error: { reason: firstError },
    };
  }

  const { password } = parsed.data;
  const { token } = formData;

  try {
    // Using auth.api.resetPassword - Better Auth server-side API
    await auth.api.resetPassword({
      body: {
        newPassword: password,
        token,
      },
    });

    return {
      success: {
        reason: "Password reset successful! You can now login with your new password.",
      },
      error: null,
    };
  } catch (error) {
    console.error("Reset password error:", error);

    if (error instanceof APIError) {
      switch (error.status) {
        case "BAD_REQUEST":
          return {
            success: null,
            error: { reason: "Invalid or expired reset token." },
          };
        case "NOT_FOUND":
          return {
            success: null,
            error: { reason: "Reset token not found or expired." },
          };
        default:
          return {
            success: null,
            error: { reason: error.message || "Failed to reset password." },
          };
      }
    }

    return {
      success: null,
      error: { reason: "Something went wrong. Please try again." },
    };
  }
}
