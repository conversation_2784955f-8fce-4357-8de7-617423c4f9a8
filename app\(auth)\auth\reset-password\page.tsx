"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import ResetPasswordForm from "@/components/auth/reset-password-form";
import Logo from "@/components/logo";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import GoogleSignin from "@/components/google-signin";

export default function ResetPasswordPage() {
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();

  useEffect(() => {
    const tokenParam = searchParams.get("token");
    if (!tokenParam) {
      setError("Invalid or missing reset token");
      return;
    }
    setToken(tokenParam);
  }, [searchParams]);

  // Error state - invalid or missing token
  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-neutral-100">
        <div className="flex flex-col items-center w-full max-w-md gap-6">
          <div className="flex items-center gap-2 self-center font-medium">
            <div className="flex items-center justify-center">
              <Logo />
            </div>
          </div>
          <Card className="w-full">
            <CardContent className="flex flex-col gap-4 pt-6">
              <div className="text-center">
                <p className="text-red-500 mb-4">{error}</p>
                <Link href="/auth/forgot-password">
                  <Button className="w-full">
                    Request New Reset Link
                  </Button>
                </Link>
              </div>
              <div className="text-center text-sm mt-4">
                Remember your password?{" "}
                <Link
                  href="/auth/login"
                  className="text-primary underline hover:no-underline font-medium"
                >
                  Back to login
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Loading state
  if (!token) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-neutral-100">
        <div className="flex flex-col items-center w-full max-w-md gap-6">
          <div className="flex items-center gap-2 self-center font-medium">
            <div className="flex items-center justify-center">
              <Logo />
            </div>
          </div>
          <Card className="w-full">
            <CardContent className="flex flex-col gap-4 pt-6">
              <div className="text-center">
                <p className="text-muted-foreground">Loading...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Main reset password form
  return (
    <div className="flex min-h-screen items-center justify-center bg-neutral-100">
      <div className="flex flex-col items-center w-full max-w-md gap-6">
        <div className="flex items-center gap-2 self-center font-medium">
          <div className="flex items-center justify-center">
            <Logo />
          </div>
        </div>
        <Card className="w-full">
          <CardContent className="flex flex-col gap-4 pt-6">
            <ResetPasswordForm token={token} />
            <div className="flex items-center my-2">
              <div className="flex-1 h-px bg-muted-foreground/30" />
              <span className="mx-3 text-muted-foreground text-xs font-medium">
                OR
              </span>
              <div className="flex-1 h-px bg-muted-foreground/30" />
            </div>
            <div className="flex flex-row gap-2 w-full">
             <GoogleSignin />
            </div>
            <div className="text-center text-sm mt-4">
              Don&apos;t have an account?{" "}
              <Link
                href="/auth/register"
                className="text-primary underline hover:no-underline font-medium"
              >
                Create an account
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}