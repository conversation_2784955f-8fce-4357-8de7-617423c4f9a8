import EditBlogForm from "@/components/blog/blog-edit";
import { But<PERSON> } from "@/components/ui/button";
import { getBlogById } from "@/data/dal";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface EditBlogPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditBlogPage({ params }: EditBlogPageProps) {
const blog = await getBlogById((await params).id);
  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-3 justify-center">
        <div className="flex items-center">
          <Link href="/admin/blog">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </Link>
        </div>
         <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Blog Post</h1>
            <p className="text-gray-600">Update your blog post</p>
          </div>
      </div>

     <EditBlogForm data={blog} />
    </div>
  );
}
