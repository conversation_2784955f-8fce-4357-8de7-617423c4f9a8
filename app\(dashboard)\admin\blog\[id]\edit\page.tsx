"use client";

import { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Save, Eye, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { updateBlog } from "@/app/(dashboard)/admin/blog/actions";
import { UpdateBlogSchema, UpdateBlogFormData } from "@/lib/schema";
import RichTextEditor from "@/components/ui/rich-text-editor";
import ImageUpload from "@/components/ui/image-upload";
import Link from "next/link";
import { getBlogById } from "@/data/dal";

interface EditBlogPageProps {
  params: {
    id: string;
  };
}

export default function EditBlogPage({ params }: EditBlogPageProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(true);
  const [content, setContent] = useState("");
  const [image, setImage] = useState("");
  const [blog, setBlog] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<UpdateBlogFormData>({
    resolver: zodResolver(UpdateBlogSchema),
    defaultValues: {
      id: params.id,
      title: "",
      description: "",
      content: "",
      image: "",
      published: false,
    },
  });

  const published = watch("published");

  // Load blog data
  useEffect(() => {
    const loadBlog = async () => {
      try {
        const response = await fetch(`/api/blog/${params.id}`);
        if (!response.ok) {
          throw new Error("Failed to load blog");
        }
        
        const blogData = await response.json();
        setBlog(blogData);
        
        // Populate form
        reset({
          id: params.id,
          title: blogData.title,
          description: blogData.description,
          content: blogData.content || "",
          image: blogData.image || "",
          published: blogData.published,
        });
        
        setContent(blogData.content || "");
        setImage(blogData.image || "");
      } catch (error) {
        console.error("Error loading blog:", error);
        toast.error("Failed to load blog post");
        router.push("/admin/blog");
      } finally {
        setIsLoading(false);
      }
    };

    loadBlog();
  }, [params.id, reset, router]);

  const onSubmit = async (data: UpdateBlogFormData) => {
    startTransition(async () => {
      try {
        const result = await updateBlog({
          ...data,
          content,
          image,
        });

        if (result.success) {
          toast.success(result.success.reason);
          router.push("/admin/blog");
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        toast.error("Failed to update blog post");
      }
    });
  };

  const handleSaveAsDraft = () => {
    setValue("published", false);
    handleSubmit(onSubmit)();
  };

  const handlePublish = () => {
    setValue("published", true);
    handleSubmit(onSubmit)();
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading blog post...</span>
        </div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Blog Post Not Found</h1>
          <Link href="/admin/blog">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/blog">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Blog Post</h1>
            <p className="text-gray-600">Update your blog post</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Title */}
            <Card>
              <CardHeader>
                <CardTitle>Post Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    {...register("title")}
                    placeholder="Enter blog post title"
                    disabled={isPending}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Enter a brief description of your blog post"
                    rows={3}
                    disabled={isPending}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Content */}
            <Card>
              <CardHeader>
                <CardTitle>Content *</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextEditor
                  content={content}
                  onChange={setContent}
                  placeholder="Start writing your blog post..."
                />
                {errors.content && (
                  <p className="text-sm text-red-600 mt-1">{errors.content.message}</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="published"
                    checked={published}
                    onCheckedChange={(checked) => setValue("published", !!checked)}
                    disabled={isPending}
                  />
                  <Label htmlFor="published">Published</Label>
                </div>

                <div className="flex flex-col space-y-2">
                  <Button
                    type="button"
                    onClick={handleSaveAsDraft}
                    variant="outline"
                    disabled={isPending}
                    className="w-full"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isPending ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button
                    type="button"
                    onClick={handlePublish}
                    disabled={isPending}
                    className="w-full"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    {isPending ? "Publishing..." : "Publish Now"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Featured Image */}
            <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  value={image}
                  onChange={setImage}
                  onRemove={() => setImage("")}
                  disabled={isPending}
                />
              </CardContent>
            </Card>

            {/* Post Info */}
            <Card>
              <CardHeader>
                <CardTitle>Post Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-gray-600">
                <div>
                  <strong>Created:</strong> {new Date(blog.createdAt).toLocaleDateString()}
                </div>
                <div>
                  <strong>Updated:</strong> {new Date(blog.updatedAt).toLocaleDateString()}
                </div>
                <div>
                  <strong>Author:</strong> {blog.author.name || blog.author.email}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
