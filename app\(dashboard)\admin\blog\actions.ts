"use server";

import { prisma } from "@/lib/prisma";
import { getSession } from "@/data/get-session";
import { 
  ActionResult, 
  CreateBlogFormData, 
  CreateBlogSchema, 
  UpdateBlogFormData, 
  UpdateBlogSchema,
  DeleteBlogFormData,
  DeleteBlogSchema 
} from "@/lib/schema";
import { revalidatePath } from "next/cache";

// Helper function to check admin permissions
async function checkAdminPermissions() {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    throw new Error("Unauthorized: Admin access required");
  }
  return session;
}

export async function createBlog(
  formData: CreateBlogFormData
): Promise<ActionResult<{ blog: { id: string; title: string } }>> {
  try {
    // Check permissions
    const session = await checkAdminPermissions();

    // Validate input
    const parsed = CreateBlogSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { 
          reason: parsed.error.flatten().fieldErrors.title?.[0] || 
                 parsed.error.flatten().fieldErrors.description?.[0] || 
                 parsed.error.flatten().fieldErrors.content?.[0] ||
                 "Invalid input data"
        },
      };
    }

    const { title, description, content, image, published } = parsed.data;

    // Create blog
    const newBlog = await prisma.blog.create({
      data: {
        title,
        description,
        content,
        image,
        published,
        authorId: session.user.id,
      },
    });

    // Revalidate the blog pages
    revalidatePath("/admin/blog");

    return {
      success: { reason: "Blog created successfully" },
      error: null,
      data: { blog: { id: newBlog.id, title: newBlog.title } },
    };
  } catch (error) {
    console.error("Create blog error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to create blog" },
    };
  }
}

export async function updateBlog(
  formData: UpdateBlogFormData
): Promise<ActionResult<{ blog: { id: string; title: string } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = UpdateBlogSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { 
          reason: parsed.error.flatten().fieldErrors.title?.[0] || 
                 parsed.error.flatten().fieldErrors.description?.[0] || 
                 parsed.error.flatten().fieldErrors.content?.[0] ||
                 "Invalid input data"
        },
      };
    }

    const { id, title, description, content, image, published } = parsed.data;

    // Check if blog exists
    const existingBlog = await prisma.blog.findUnique({
      where: { id },
    });

    if (!existingBlog) {
      return {
        success: null,
        error: { reason: "Blog not found" },
      };
    }

    // Update blog
    const updatedBlog = await prisma.blog.update({
      where: { id },
      data: {
        title,
        description,
        content,
        image,
        published,
        updatedAt: new Date(),
      },
    });

    // Revalidate the blog pages
    revalidatePath("/admin/blog");
    revalidatePath(`/admin/blog/${id}/edit`);

    return {
      success: { reason: "Blog updated successfully" },
      error: null,
      data: { blog: { id: updatedBlog.id, title: updatedBlog.title } },
    };
  } catch (error) {
    console.error("Update blog error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to update blog" },
    };
  }
}

export async function deleteBlog(
  formData: DeleteBlogFormData
): Promise<ActionResult> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = DeleteBlogSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { reason: "Invalid blog ID" },
      };
    }

    const { id } = parsed.data;

    // Check if blog exists
    const existingBlog = await prisma.blog.findUnique({
      where: { id },
    });

    if (!existingBlog) {
      return {
        success: null,
        error: { reason: "Blog not found" },
      };
    }

    // Delete blog
    await prisma.blog.delete({
      where: { id },
    });

    // Revalidate the blog pages
    revalidatePath("/admin/blog");

    return {
      success: { reason: "Blog deleted successfully" },
      error: null,
    };
  } catch (error) {
    console.error("Delete blog error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to delete blog" },
    };
  }
}

// Toggle blog published status
export async function toggleBlogPublished(
  id: string
): Promise<ActionResult<{ blog: { id: string; published: boolean } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Check if blog exists
    const existingBlog = await prisma.blog.findUnique({
      where: { id },
    });

    if (!existingBlog) {
      return {
        success: null,
        error: { reason: "Blog not found" },
      };
    }

    // Toggle published status
    const updatedBlog = await prisma.blog.update({
      where: { id },
      data: {
        published: !existingBlog.published,
        updatedAt: new Date(),
      },
    });

    // Revalidate the blog pages
    revalidatePath("/admin/blog");

    return {
      success: { 
        reason: `Blog ${updatedBlog.published ? 'published' : 'unpublished'} successfully` 
      },
      error: null,
      data: { blog: { id: updatedBlog.id, published: updatedBlog.published } },
    };
  } catch (error) {
    console.error("Toggle blog published error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to update blog status" },
    };
  }
}
