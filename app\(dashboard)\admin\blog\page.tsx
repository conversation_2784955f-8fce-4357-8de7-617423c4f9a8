import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import {
  Book<PERSON><PERSON>,
  Eye,
  FileText,
} from "lucide-react";
import { getBlogStats } from "@/data/dal";
import BlogWrapper from "@/components/blog/blog-wrapper";
import BlogTableSkeleton from "@/components/blog/blog-table-skeleton";
import { Suspense } from "react";

interface BlogPageProps {
  searchParams: {
    page?: string;
    search?: string;
    published?: string;
  };
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const published = searchParams.published || '';

  const stats = await getBlogStats();
  const totalBlogs = stats?.totalBlogs ?? 0;
  const publishedBlogs = stats?.publishedBlogs ?? 0;
  const draftBlogs = stats?.draftBlogs ?? 0;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Blog Management</h1>
          <p className="text-gray-600">Create and manage blog posts</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBlogs}</div>
            <p className="text-xs text-muted-foreground">All blog posts</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{publishedBlogs}</div>
            <p className="text-xs text-muted-foreground">Live posts</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{draftBlogs}</div>
            <p className="text-xs text-muted-foreground">Unpublished posts</p>
          </CardContent>
        </Card>
      </div>

      {/* Blog Table */}
      <Suspense fallback={<BlogTableSkeleton />}>
        <BlogWrapper
          page={page}
          search={search}
          published={published}
        />
      </Suspense>
    </div>
  );
}
