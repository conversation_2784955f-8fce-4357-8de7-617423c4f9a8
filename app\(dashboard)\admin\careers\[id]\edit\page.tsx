
import EditCareerForm from "@/components/careers/career-edit";
import { But<PERSON> } from "@/components/ui/button";
import { getCareerById } from "@/data/dal";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface EditCareerPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditCareerPage({ params }: EditCareerPageProps) {
const career = await getCareerById((await params).id);
  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 justify-center">
        <div className="flex items-center">
          <Link href="/admin/careers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Careers
            </Button>
          </Link>
        </div>
         <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Career Posting</h1>
            <p className="text-gray-600">Update job opportunity details</p>
          </div>
      </div>

     <EditCareerForm data={career} />
    </div>
  );
}
