"use server";

import { prisma } from "@/lib/prisma";
import { getSession } from "@/data/get-session";
import { 
  ActionResult, 
  CreateCareerFormData, 
  CreateCareerSchema, 
  UpdateCareerFormData, 
  UpdateCareerSchema,
  DeleteCareerFormData,
  DeleteCareerSchema 
} from "@/lib/schema";
import { revalidatePath } from "next/cache";

// Helper function to check admin permissions
async function checkAdminPermissions() {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    throw new Error("Unauthorized: Admin access required");
  }
  return session;
}

export async function createCareer(
  formData: CreateCareerFormData
): Promise<ActionResult<{ career: { id: string; title: string } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = CreateCareerSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { 
          reason: parsed.error.flatten().fieldErrors.title?.[0] || 
                 parsed.error.flatten().fieldErrors.description?.[0] || 
                 parsed.error.flatten().fieldErrors.requirements?.[0] ||
                 "Invalid input data"
        },
      };
    }

    const { title, description, requirements, location, type, salary, active } = parsed.data;

    // Create career
    const newCareer = await prisma.career.create({
      data: {
        title,
        description,
        requirements,
        location,
        type,
        salary,
        active,
      },
    });

    // Revalidate the career pages
    revalidatePath("/admin/careers");

    return {
      success: { reason: "Career posting created successfully" },
      error: null,
      data: { career: { id: newCareer.id, title: newCareer.title } },
    };
  } catch (error) {
    console.error("Create career error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to create career posting" },
    };
  }
}

export async function updateCareer(
  formData: UpdateCareerFormData
): Promise<ActionResult<{ career: { id: string; title: string } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = UpdateCareerSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { 
          reason: parsed.error.flatten().fieldErrors.title?.[0] || 
                 parsed.error.flatten().fieldErrors.description?.[0] || 
                 parsed.error.flatten().fieldErrors.requirements?.[0] ||
                 "Invalid input data"
        },
      };
    }

    const { id, title, description, requirements, location, type, salary, active } = parsed.data;

    // Check if career exists
    const existingCareer = await prisma.career.findUnique({
      where: { id },
    });

    if (!existingCareer) {
      return {
        success: null,
        error: { reason: "Career posting not found" },
      };
    }

    // Update career
    const updatedCareer = await prisma.career.update({
      where: { id },
      data: {
        title,
        description,
        requirements,
        location,
        type,
        salary,
        active,
        updatedAt: new Date(),
      },
    });

    // Revalidate the career pages
    revalidatePath("/admin/careers");
    revalidatePath(`/admin/careers/${id}/edit`);

    return {
      success: { reason: "Career posting updated successfully" },
      error: null,
      data: { career: { id: updatedCareer.id, title: updatedCareer.title } },
    };
  } catch (error) {
    console.error("Update career error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to update career posting" },
    };
  }
}

export async function deleteCareer(
  formData: DeleteCareerFormData
): Promise<ActionResult> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = DeleteCareerSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { reason: "Invalid career ID" },
      };
    }

    const { id } = parsed.data;

    // Check if career exists
    const existingCareer = await prisma.career.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    if (!existingCareer) {
      return {
        success: null,
        error: { reason: "Career posting not found" },
      };
    }

    // Check if there are applications
    if (existingCareer._count.applications > 0) {
      return {
        success: null,
        error: { reason: `Cannot delete career posting with ${existingCareer._count.applications} application(s). Please review applications first.` },
      };
    }

    // Delete career
    await prisma.career.delete({
      where: { id },
    });

    // Revalidate the career pages
    revalidatePath("/admin/careers");

    return {
      success: { reason: "Career posting deleted successfully" },
      error: null,
    };
  } catch (error) {
    console.error("Delete career error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to delete career posting" },
    };
  }
}

// Toggle career active status
export async function toggleCareerActive(
  id: string
): Promise<ActionResult<{ career: { id: string; active: boolean } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Check if career exists
    const existingCareer = await prisma.career.findUnique({
      where: { id },
    });

    if (!existingCareer) {
      return {
        success: null,
        error: { reason: "Career posting not found" },
      };
    }

    // Toggle active status
    const updatedCareer = await prisma.career.update({
      where: { id },
      data: {
        active: !existingCareer.active,
        updatedAt: new Date(),
      },
    });

    // Revalidate the career pages
    revalidatePath("/admin/careers");

    return {
      success: { 
        reason: `Career posting ${updatedCareer.active ? 'activated' : 'deactivated'} successfully` 
      },
      error: null,
      data: { career: { id: updatedCareer.id, active: updatedCareer.active } },
    };
  } catch (error) {
    console.error("Toggle career active error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to update career status" },
    };
  }
}
