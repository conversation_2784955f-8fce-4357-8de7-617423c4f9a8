"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, Eye } from "lucide-react";
import { toast } from "sonner";
import { createCareer } from "@/app/(dashboard)/admin/careers/actions";
import { CreateCareerSchema, CreateCareerFormData } from "@/lib/schema";
import RichTextEditor from "@/components/ui/rich-text-editor";
import Link from "next/link";

const jobTypes = [
  "Full-time",
  "Part-time",
  "Contract",
  "Temporary",
  "Internship",
  "Remote",
  "Hybrid",
];

export default function CreateCareerPage() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [description, setDescription] = useState("");
  const [requirements, setRequirements] = useState("");

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CreateCareerFormData>({
    resolver: zodResolver(CreateCareerSchema),
    mode: "onSubmit", // Only validate on submit, not on change
    defaultValues: {
      title: "",
      description: "",
      requirements: "",
      location: "",
      type: "",
      salary: "",
      active: true,
    },
  });

  const active = watch("active");

  // Update form content when rich text editor changes
  const handleDescriptionChange = (newContent: string) => {
    setDescription(newContent);
    setValue("description", newContent);
  };

  const handleRequirementsChange = (newContent: string) => {
    setRequirements(newContent);
    setValue("requirements", newContent);
  };

  const onSubmit = async (data: CreateCareerFormData) => {
    startTransition(async () => {
      try {
        const result = await createCareer({
          ...data,
          description,
          requirements,
        });

        if (result.success) {
          toast.success(result.success.reason);
          router.push("/admin/careers");
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Create career error:", error);
        toast.error("Failed to create career posting");
      }
    });
  };

  const handleSaveAsDraft = () => {
    setValue("active", false);
    handleSubmit(onSubmit)();
  };

  const handlePublish = () => {
    setValue("active", true);
    handleSubmit(onSubmit)();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 justify-center">
        <div className="flex items-center">
          <Link href="/admin/careers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Careers
            </Button>
          </Link>
          
        </div>
        <div>
            <h1 className="text-3xl font-bold text-gray-900">Create New Career Posting</h1>
            <p className="text-gray-600">Post a new job opportunity</p>
          </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Job Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    {...register("title")}
                    placeholder="Enter job title"
                    disabled={isPending}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      {...register("location")}
                      placeholder="e.g., New York, NY or Remote"
                      disabled={isPending}
                    />
                    {errors.location && (
                      <p className="text-sm text-red-600 mt-1">{errors.location.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">Job Type</Label>
                    <Select onValueChange={(value) => setValue("type", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select job type" />
                      </SelectTrigger>
                      <SelectContent>
                        {jobTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className="text-sm text-red-600 mt-1">{errors.type.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="salary">Salary Range</Label>
                  <Input
                    id="salary"
                    {...register("salary")}
                    placeholder="e.g., $70,000 - $90,000 or Competitive"
                    disabled={isPending}
                  />
                  {errors.salary && (
                    <p className="text-sm text-red-600 mt-1">{errors.salary.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Job Description */}
            <Card>
              <CardHeader>
                <CardTitle>Job Description *</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextEditor
                  content={description}
                  onChange={handleDescriptionChange}
                  placeholder="Describe the role, responsibilities, and what makes this position exciting..."
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                )}
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card>
              <CardHeader>
                <CardTitle>Requirements *</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextEditor
                  content={requirements}
                  onChange={handleRequirementsChange}
                  placeholder="List the required skills, experience, education, and qualifications..."
                />
                {errors.requirements && (
                  <p className="text-sm text-red-600 mt-1">{errors.requirements.message}</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="active"
                    checked={active}
                    onCheckedChange={(checked) => setValue("active", !!checked)}
                    disabled={isPending}
                  />
                  <Label htmlFor="active">Publish immediately</Label>
                </div>

                <div className="flex flex-col space-y-2">
                  <Button
                    type="button"
                    onClick={handleSaveAsDraft}
                    variant="outline"
                    disabled={isPending}
                    className="w-full"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isPending ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button
                    type="button"
                    onClick={handlePublish}
                    disabled={isPending}
                    className="w-full"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    {isPending ? "Publishing..." : "Publish Now"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle>Tips for Great Job Postings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm text-gray-600">
                <div>
                  <strong>Clear Title:</strong> Use specific, searchable job titles
                </div>
                <div>
                  <strong>Detailed Description:</strong> Include day-to-day responsibilities
                </div>
                <div>
                  <strong>Specific Requirements:</strong> List must-have vs. nice-to-have skills
                </div>
                <div>
                  <strong>Company Culture:</strong> Highlight what makes your team special
                </div>
                <div>
                  <strong>Growth Opportunities:</strong> Mention career development paths
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
