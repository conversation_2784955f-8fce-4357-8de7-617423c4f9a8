import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import {
  Briefcase,
  Eye,
  Users,
  Clock,
} from "lucide-react";
import { getCareerStats } from "@/data/dal";
import CareerWrapper from "@/components/careers/career-wrapper";
import CareerTableSkeleton from "@/components/careers/career-table-skeleton";
import { Suspense } from "react";

interface CareersPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
    active?: string;
  }>;
}

export default async function CareersPage({ searchParams }: CareersPageProps) {
   const page = parseInt((await searchParams).page || '1');
  const search = (await searchParams).search || '';
  const active = (await searchParams).active || '';

  const stats = await getCareerStats();
  const totalCareers = stats?.totalCareers ?? 0;
  const activeCareers = stats?.activeCareers ?? 0;
  const inactiveCareers = stats?.inactiveCareers ?? 0;
  const totalApplications = stats?.totalApplications ?? 0;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Career Management</h1>
          <p className="text-gray-600">Manage job postings and applications</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCareers}</div>
            <p className="text-xs text-muted-foreground">All job postings</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeCareers}</div>
            <p className="text-xs text-muted-foreground">Currently hiring</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Jobs</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inactiveCareers}</div>
            <p className="text-xs text-muted-foreground">Not currently hiring</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalApplications}</div>
            <p className="text-xs text-muted-foreground">Total received</p>
          </CardContent>
        </Card>
      </div>

      {/* Career Table */}
      <Suspense fallback={<CareerTableSkeleton />}>
        <CareerWrapper
          page={page}
          search={search}
          active={active}
        />
      </Suspense>
    </div>
  );
}
