"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Briefcase,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Calendar,
  MapPin,
  DollarSign,
  Users,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react";

// Mock data - replace with real data from your API
const mockJobs = [
  {
    id: "1",
    title: "Frontend Developer",
    description: "We're looking for a talented Frontend Developer to join our team...",
    requirements: "3+ years experience with React, TypeScript, and modern web technologies",
    location: "Remote",
    type: "Full-time",
    salary: "$70,000 - $90,000",
    active: true,
    postedDate: "2024-01-15",
    applications: 12,
  },
  {
    id: "2",
    title: "Marketing Manager",
    description: "Join our marketing team to drive growth and brand awareness...",
    requirements: "5+ years in digital marketing, experience with social media and content strategy",
    location: "New York, NY",
    type: "Full-time",
    salary: "$80,000 - $100,000",
    active: true,
    postedDate: "2024-01-10",
    applications: 8,
  },
  {
    id: "3",
    title: "Customer Support Specialist",
    description: "Help our customers succeed with our products...",
    requirements: "2+ years customer service experience, excellent communication skills",
    location: "Remote",
    type: "Part-time",
    salary: "$35,000 - $45,000",
    active: false,
    postedDate: "2024-01-05",
    applications: 15,
  },
];

const mockApplications = [
  {
    id: "1",
    jobId: "1",
    jobTitle: "Frontend Developer",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "******-0123",
    status: "PENDING",
    appliedAt: "2024-01-16",
    resume: "/resumes/john-doe.pdf",
    coverLetter: "I am excited to apply for the Frontend Developer position...",
  },
  {
    id: "2",
    jobId: "1",
    jobTitle: "Frontend Developer",
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "******-0124",
    status: "REVIEWING",
    appliedAt: "2024-01-15",
    resume: "/resumes/jane-smith.pdf",
    coverLetter: "With 5 years of React experience, I believe I would be a great fit...",
  },
  {
    id: "3",
    jobId: "2",
    jobTitle: "Marketing Manager",
    firstName: "Mike",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "******-0125",
    status: "INTERVIEWED",
    appliedAt: "2024-01-12",
    resume: "/resumes/mike-johnson.pdf",
    coverLetter: "I have successfully led marketing campaigns that increased revenue by 40%...",
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "PENDING": return "bg-yellow-100 text-yellow-800";
    case "REVIEWING": return "bg-blue-100 text-blue-800";
    case "INTERVIEWED": return "bg-purple-100 text-purple-800";
    case "ACCEPTED": return "bg-green-100 text-green-800";
    case "REJECTED": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case "PENDING": return <Clock className="w-3 h-3" />;
    case "REVIEWING": return <Eye className="w-3 h-3" />;
    case "INTERVIEWED": return <Users className="w-3 h-3" />;
    case "ACCEPTED": return <CheckCircle className="w-3 h-3" />;
    case "REJECTED": return <XCircle className="w-3 h-3" />;
    default: return <Clock className="w-3 h-3" />;
  }
};

export default function CareersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [selectedApplication, setSelectedApplication] = useState<any>(null);
  const [isCreateJobDialogOpen, setIsCreateJobDialogOpen] = useState(false);
  const [isEditJobDialogOpen, setIsEditJobDialogOpen] = useState(false);
  const [jobFormData, setJobFormData] = useState({
    title: "",
    description: "",
    requirements: "",
    location: "",
    type: "",
    salary: "",
    active: true,
  });

  const filteredJobs = mockJobs.filter((job) =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredApplications = mockApplications.filter((app) =>
    app.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.jobTitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalJobs = mockJobs.length;
  const activeJobs = mockJobs.filter(job => job.active).length;
  const totalApplications = mockApplications.length;
  const pendingApplications = mockApplications.filter(app => app.status === "PENDING").length;

  const handleCreateJob = () => {
    console.log("Creating job:", jobFormData);
    setIsCreateJobDialogOpen(false);
    setJobFormData({ title: "", description: "", requirements: "", location: "", type: "", salary: "", active: true });
  };

  const handleEditJob = () => {
    console.log("Editing job:", jobFormData);
    setIsEditJobDialogOpen(false);
    setJobFormData({ title: "", description: "", requirements: "", location: "", type: "", salary: "", active: true });
  };

  const openEditJobDialog = (job: any) => {
    setSelectedJob(job);
    setJobFormData({
      title: job.title,
      description: job.description,
      requirements: job.requirements,
      location: job.location,
      type: job.type,
      salary: job.salary,
      active: job.active,
    });
    setIsEditJobDialogOpen(true);
  };

  const updateApplicationStatus = (applicationId: string, newStatus: string) => {
    console.log(`Updating application ${applicationId} to status: ${newStatus}`);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Careers Management</h1>
          <p className="text-gray-600">Manage job postings and applications</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalJobs}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeJobs}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalApplications}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingApplications}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Jobs and Applications */}
      <Tabs defaultValue="jobs" className="space-y-6">
        <TabsList>
          <TabsTrigger value="jobs">Job Postings</TabsTrigger>
          <TabsTrigger value="applications">Applications</TabsTrigger>
        </TabsList>

        {/* Jobs Tab */}
        <TabsContent value="jobs">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Job Postings</CardTitle>
              <Dialog open={isCreateJobDialogOpen} onOpenChange={setIsCreateJobDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Post New Job
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Create New Job Posting</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="job-title">Job Title</Label>
                      <Input
                        id="job-title"
                        value={jobFormData.title}
                        onChange={(e) => setJobFormData({ ...jobFormData, title: e.target.value })}
                        placeholder="Enter job title"
                      />
                    </div>
                    <div>
                      <Label htmlFor="job-description">Description</Label>
                      <Textarea
                        id="job-description"
                        value={jobFormData.description}
                        onChange={(e) => setJobFormData({ ...jobFormData, description: e.target.value })}
                        placeholder="Enter job description"
                        rows={4}
                      />
                    </div>
                    <div>
                      <Label htmlFor="job-requirements">Requirements</Label>
                      <Textarea
                        id="job-requirements"
                        value={jobFormData.requirements}
                        onChange={(e) => setJobFormData({ ...jobFormData, requirements: e.target.value })}
                        placeholder="Enter job requirements"
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="job-location">Location</Label>
                        <Input
                          id="job-location"
                          value={jobFormData.location}
                          onChange={(e) => setJobFormData({ ...jobFormData, location: e.target.value })}
                          placeholder="e.g., Remote, New York, NY"
                        />
                      </div>
                      <div>
                        <Label htmlFor="job-type">Job Type</Label>
                        <Select value={jobFormData.type} onValueChange={(value) => setJobFormData({ ...jobFormData, type: value })}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select job type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Full-time">Full-time</SelectItem>
                            <SelectItem value="Part-time">Part-time</SelectItem>
                            <SelectItem value="Contract">Contract</SelectItem>
                            <SelectItem value="Internship">Internship</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="job-salary">Salary Range</Label>
                      <Input
                        id="job-salary"
                        value={jobFormData.salary}
                        onChange={(e) => setJobFormData({ ...jobFormData, salary: e.target.value })}
                        placeholder="e.g., $70,000 - $90,000"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="job-active"
                        checked={jobFormData.active}
                        onChange={(e) => setJobFormData({ ...jobFormData, active: e.target.checked })}
                      />
                      <Label htmlFor="job-active">Active (visible to applicants)</Label>
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setIsCreateJobDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleCreateJob}>Post Job</Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search jobs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Title</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Applications</TableHead>
                    <TableHead>Posted</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredJobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{job.title}</p>
                          <p className="text-sm text-gray-600 flex items-center">
                            <DollarSign className="w-3 h-3 mr-1" />
                            {job.salary}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {job.location}
                        </div>
                      </TableCell>
                      <TableCell>{job.type}</TableCell>
                      <TableCell>
                        <Badge className={job.active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                          {job.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>{job.applications}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {job.postedDate}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditJobDialog(job)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Applications Tab */}
        <TabsContent value="applications">
          <Card>
            <CardHeader>
              <CardTitle>Job Applications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search applications..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Applicant</TableHead>
                    <TableHead>Job</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Applied</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{application.firstName} {application.lastName}</p>
                          <p className="text-sm text-gray-600">{application.email}</p>
                        </div>
                      </TableCell>
                      <TableCell>{application.jobTitle}</TableCell>
                      <TableCell>
                        <Select
                          value={application.status}
                          onValueChange={(value) => updateApplicationStatus(application.id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="REVIEWING">Reviewing</SelectItem>
                            <SelectItem value="INTERVIEWED">Interviewed</SelectItem>
                            <SelectItem value="ACCEPTED">Accepted</SelectItem>
                            <SelectItem value="REJECTED">Rejected</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{application.appliedAt}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedApplication(application)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Application Details</DialogTitle>
                            </DialogHeader>
                            {selectedApplication && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <h4 className="font-medium">Applicant Information</h4>
                                    <p>{selectedApplication.firstName} {selectedApplication.lastName}</p>
                                    <p className="text-sm text-gray-600">{selectedApplication.email}</p>
                                    <p className="text-sm text-gray-600">{selectedApplication.phone}</p>
                                  </div>
                                  <div>
                                    <h4 className="font-medium">Application Details</h4>
                                    <p>Job: {selectedApplication.jobTitle}</p>
                                    <p>Applied: {selectedApplication.appliedAt}</p>
                                    <Badge className={`${getStatusColor(selectedApplication.status)} flex items-center space-x-1 w-fit`}>
                                      {getStatusIcon(selectedApplication.status)}
                                      <span>{selectedApplication.status}</span>
                                    </Badge>
                                  </div>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-2">Cover Letter</h4>
                                  <p className="text-sm bg-gray-50 p-3 rounded">{selectedApplication.coverLetter}</p>
                                </div>
                                {selectedApplication.resume && (
                                  <div>
                                    <h4 className="font-medium mb-2">Resume</h4>
                                    <Button variant="outline" size="sm">
                                      <Eye className="w-4 h-4 mr-2" />
                                      View Resume
                                    </Button>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Job Dialog */}
      <Dialog open={isEditJobDialogOpen} onOpenChange={setIsEditJobDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Job Posting</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-job-title">Job Title</Label>
              <Input
                id="edit-job-title"
                value={jobFormData.title}
                onChange={(e) => setJobFormData({ ...jobFormData, title: e.target.value })}
                placeholder="Enter job title"
              />
            </div>
            <div>
              <Label htmlFor="edit-job-description">Description</Label>
              <Textarea
                id="edit-job-description"
                value={jobFormData.description}
                onChange={(e) => setJobFormData({ ...jobFormData, description: e.target.value })}
                placeholder="Enter job description"
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="edit-job-requirements">Requirements</Label>
              <Textarea
                id="edit-job-requirements"
                value={jobFormData.requirements}
                onChange={(e) => setJobFormData({ ...jobFormData, requirements: e.target.value })}
                placeholder="Enter job requirements"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-job-location">Location</Label>
                <Input
                  id="edit-job-location"
                  value={jobFormData.location}
                  onChange={(e) => setJobFormData({ ...jobFormData, location: e.target.value })}
                  placeholder="e.g., Remote, New York, NY"
                />
              </div>
              <div>
                <Label htmlFor="edit-job-type">Job Type</Label>
                <Select value={jobFormData.type} onValueChange={(value) => setJobFormData({ ...jobFormData, type: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select job type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Full-time">Full-time</SelectItem>
                    <SelectItem value="Part-time">Part-time</SelectItem>
                    <SelectItem value="Contract">Contract</SelectItem>
                    <SelectItem value="Internship">Internship</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="edit-job-salary">Salary Range</Label>
              <Input
                id="edit-job-salary"
                value={jobFormData.salary}
                onChange={(e) => setJobFormData({ ...jobFormData, salary: e.target.value })}
                placeholder="e.g., $70,000 - $90,000"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-job-active"
                checked={jobFormData.active}
                onChange={(e) => setJobFormData({ ...jobFormData, active: e.target.checked })}
              />
              <Label htmlFor="edit-job-active">Active (visible to applicants)</Label>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditJobDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditJob}>Update Job</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
