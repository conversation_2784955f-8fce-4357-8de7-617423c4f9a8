import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth.api.getSession({
    headers: await headers()
  });

  // Check if user has admin role
  if (session?.user.role !== "ADMIN") {
    return redirect("/");
  }

  return <>{children}</>;
}
