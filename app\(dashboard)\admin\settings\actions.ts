/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { prisma } from "@/lib/prisma";
import { getSession } from "@/data/get-session";
import {
  ActionResult,
  CreateUserFormData,
  CreateUserSchema,
  UpdateUserFormData,
  UpdateUserSchema,
  DeleteUserFormData,
  DeleteUserSchema
} from "@/lib/schema";
import { revalidatePath } from "next/cache";
import { auth } from "@/lib/auth";
import { APIError } from "better-auth/api";
// We'll use Better Auth's built-in password hashing

// Helper function to check admin permissions
async function checkAdminPermissions() {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    throw new Error("Unauthorized: Admin access required");
  }
  return session;
}

export async function createUser(
  formData: CreateUserFormData
): Promise<ActionResult<{ user: { id: string; email: string } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = CreateUserSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: {
          reason: parsed.error.flatten().fieldErrors.email?.[0] ||
                 parsed.error.flatten().fieldErrors.name?.[0] ||
                 parsed.error.flatten().fieldErrors.password?.[0] ||
                 "Invalid input data"
        },
      };
    }

    const { name, email, password, role } = parsed.data;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return {
        success: null,
        error: { reason: "User with this email already exists" },
      };
    }

    // Use Better Auth's signUpEmail to create user with proper password hashing
    await auth.api.signUpEmail({
      body: {
        email,
        password,
        name,
      },
    });

    // Update the user's role after creation
    const newUser = await prisma.user.update({
      where: { email },
      data: {
        role: role as "CUSTOMER" | "ADMIN",
      },
    });

    // Revalidate the settings page
    revalidatePath("/admin/settings");

    return {
      success: { reason: "User created successfully" },
      error: null,
      data: { user: { id: newUser.id, email: newUser.email } },
    };
  } catch (error) {
    console.error("Create user error:", error);
    if (error instanceof APIError) {
      switch (error.status) {
        case "UNPROCESSABLE_ENTITY":
          return { error: { reason: "User already exists." }, success: null };
        case "BAD_REQUEST":
          return { error: { reason: "Invalid email." }, success: null };
        default:
          return { error: { reason: "Something went wrong." }, success: null };
      }
    }
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to create user" },
    };
  }
}

export async function updateUser(
  formData: UpdateUserFormData
): Promise<ActionResult<{ user: { id: string; email: string } }>> {
  try {
    // Check permissions
    await checkAdminPermissions();

    // Validate input
    const parsed = UpdateUserSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: {
          reason: parsed.error.flatten().fieldErrors.email?.[0] ||
                 parsed.error.flatten().fieldErrors.name?.[0] ||
                 "Invalid input data"
        },
      };
    }

    const { id, name, email, role } = parsed.data;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return {
        success: null,
        error: { reason: "User not found" },
      };
    }

    // Check if email is already taken by another user
    if (email !== existingUser.email) {
      const emailTaken = await prisma.user.findUnique({
        where: { email },
      });

      if (emailTaken) {
        return {
          success: null,
          error: { reason: "Email is already taken by another user" },
        };
      }
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        name,
        email,
        role: role as "CUSTOMER" | "ADMIN",
      },
    });

    // Revalidate the settings page
    revalidatePath("/admin/settings");

    return {
      success: { reason: "User updated successfully" },
      error: null,
      data: { user: { id: updatedUser.id, email: updatedUser.email } },
    };
  } catch (error) {
    console.error("Update user error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to update user" },
    };
  }
}

export async function deleteUser(
  formData: DeleteUserFormData
): Promise<ActionResult> {
  try {
    // Check permissions
    const session = await checkAdminPermissions();

    // Validate input
    const parsed = DeleteUserSchema.safeParse(formData);
    if (!parsed.success) {
      return {
        success: null,
        error: { reason: "Invalid user ID" },
      };
    }

    const { id } = parsed.data;

    // Prevent admin from deleting themselves
    if (session.user.id === id) {
      return {
        success: null,
        error: { reason: "You cannot delete your own account" },
      };
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return {
        success: null,
        error: { reason: "User not found" },
      };
    }

    // Delete user (cascade will handle related records)
    await prisma.user.delete({
      where: { id },
    });

    // Revalidate the settings page
    revalidatePath("/admin/settings");

    return {
      success: { reason: "User deleted successfully" },
      error: null,
    };
  } catch (error) {
    console.error("Delete user error:", error);
    return {
      success: null,
      error: { reason: error instanceof Error ? error.message : "Failed to delete user" },
    };
  }
}

// Get users with pagination and search
export async function getUsersWithPagination({
  page = 1,
  limit = 10,
  search = "",
  role = "",
}: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
} = {}) {
  try {
    // Check permissions
    await checkAdminPermissions();

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role && (role === "ADMIN" || role === "CUSTOMER")) {
      where.role = role;
    }

    // Get users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          lastLoginAt: true,
          emailVerified: true,
        },
      }),
      prisma.user.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      users,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Get users error:", error);
    throw new Error("Failed to fetch users");
  }
}
