
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  User,
  Shield,
} from "lucide-react";
import SettingsDialog from "@/components/settings/settings-dialog";
import { getUsers, getUserStats } from "@/data/dal";
import type { User as PrismaUser } from "@/generated/prisma";
import SettingsTable from "@/components/settings/settings-table";
import { Suspense } from "react";

export default async function SettingsPage() {
  const stats = await getUserStats();
  const totalUsers = stats?.totalUsers ?? 0;
  const adminUsers = stats?.adminUsers ?? 0;
  const customerUsers = stats?.customerUsers ?? 0;
  const users:PrismaUser[] = (await getUsers()) ?? [];



  

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">
            Manage system settings and user accounts
          </p>
        </div>
      </div>

      {/* Tabs for Users and System Settings */}
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalUsers}
                </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Admin Users</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminUsers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customerUsers}</div>
            </CardContent>
          </Card>
        </div>

<Suspense fallback={<div>Loading...</div>}>

        <SettingsTable users={users} />
</Suspense>
      </div>
      {/* Edit User Dialog */}
      <SettingsDialog />
    </div>
  );
}
