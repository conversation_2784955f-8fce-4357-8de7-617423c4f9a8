import Navigation from "@/components/layout/navigation";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
   const session = await auth.api.getSession({
        headers: await headers()
    })

    if(!session?.user) {
        console.log("No session found in dashboard layout, redirecting to login");
        return redirect("/auth/login");
    }

    console.log("Dashboard layout session:", {
      userId: session.user.id,
      email: session.user.email,
      role: session.user.role || "CUSTOMER"
    });

  return (
    <>
      <Navigation />
      <main className="min-h-screen bg-gray-50">
        {children}
      </main>
    </>
  );
}
