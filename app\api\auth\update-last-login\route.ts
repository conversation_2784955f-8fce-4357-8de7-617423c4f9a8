import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { headers } from "next/headers";

export async function POST() {
  try {
    // Get the current session using Better Auth
    const session = await auth.api.getSession({
      headers: await headers()
    });
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Update the lastLoginAt field for the current user
    await prisma.user.update({
      where: { id: session.user.id },
      data: { lastLoginAt: new Date() },
    });

    return NextResponse.json({ 
      success: true,
      message: "Last login time updated successfully"
    });
  } catch (error) {
    console.error("Failed to update lastLoginAt:", error);
    return NextResponse.json(
      { error: "Failed to update last login time" },
      { status: 500 }
    );
  }
}
