import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/data/get-session";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await getSession();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Update the lastLoginAt field for the current user
    await prisma.user.update({
      where: { id: session.user.id },
      data: { lastLoginAt: new Date() },
    });

    console.log(`Updated lastLoginAt for user: ${session.user.email}`);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to update lastLoginAt:", error);
    return NextResponse.json(
      { error: "Failed to update last login time" },
      { status: 500 }
    );
  }
}
