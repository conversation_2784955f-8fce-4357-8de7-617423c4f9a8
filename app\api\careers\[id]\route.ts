import { NextRequest, NextResponse } from "next/server";
import { getCareerById } from "@/data/dal";

export async function GET(
  request: NextRequest,
 { params }: { params: Promise<{ id: string }> }
) {
  try {
    const career = await getCareerById((await params).id);
    
    if (!career) {
      return NextResponse.json(
        { error: "Career posting not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(career);
  } catch (error) {
    console.error("Error fetching career:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
