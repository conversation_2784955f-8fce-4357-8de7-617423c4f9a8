import {auth} from "@/lib/auth"
import { S3Client } from '@aws-sdk/client-s3';
import {
  createUploadRouteHandler,
  route,
  UploadFileError,
  type Router,
} from 'better-upload/server';
import { headers } from "next/headers";

const s3 = new S3Client(); 
const router: Router = {
  client: s3,
  bucketName: 'my-bucket', 
  routes: {
    demo: route({
      fileTypes: ['image/*'],
      multipleFiles: true,
      maxFiles: 4,
      onBeforeUpload: async () => {
       const session = await auth.api.getSession({
        headers: await headers()
    })
        if (session?.user.role !== "ADMIN") {
          throw new UploadFileError('Unauthorized!');
        }
      },
    }),
  },
};

export const { POST } = createUploadRouteHandler(router);