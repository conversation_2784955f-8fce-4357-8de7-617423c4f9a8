import type { Metada<PERSON> } from "next";
import "./globals.css";
import DevToolbar from "@/components/dev-tools";
import { Toaster } from "@/components/ui/sonner";
import NextTopLoader from "nextjs-toploader";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <DevToolbar />
        <NextTopLoader showSpinner={false} />
        <Suspense>

        {children}
        </Suspense>
        <Toaster position="top-right" richColors />
      </body>
    </html>
  );
}
