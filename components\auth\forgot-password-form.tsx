"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ForgotPasswordFormData, ForgotPasswordSchema } from "@/lib/schema";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { forgotPassword } from "@/app/(auth)/auth/forgot-password/action";
import { toast } from "sonner";
import Link from "next/link";

export default function ForgotPasswordForm() {
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [rateLimitMessage, setRateLimitMessage] = useState("");
  const [countdown, setCountdown] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(ForgotPasswordSchema),
    defaultValues: { email: "" },
  });

  // Countdown timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (countdown > 0) {
      interval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setIsRateLimited(false);
            setRateLimitMessage("");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [countdown]);

  const startRateLimit = (seconds: number, message: string) => {
    setIsRateLimited(true);
    setCountdown(seconds);
    setRateLimitMessage(message);
  };

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      const result = await forgotPassword(data);
      if (result.success) {
        toast.success(result.success.reason);
        // Reset form and show success state with countdown
        reset();
        startRateLimit(300, "Email sent! Please check your inbox."); // 5 minutes = 300 seconds
      } else if (result.error) {
        toast.error(result.error.reason);

        // Check if it's a rate limit error
        if (result.error.reason.toLowerCase().includes("too many") ||
            result.error.reason.toLowerCase().includes("rate limit")) {
          startRateLimit(300, "Too many requests. Please wait before trying again."); // 5 minutes
        }
      }
    } catch (error) {
      console.error("Forgot password error:", error);
      toast.error("Something went wrong. Please try again.");
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex w-full flex-col gap-4"
    >
      <div className="flex flex-col gap-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          disabled={isRateLimited}
          {...register("email")}
        />
        {errors.email && (
          <span className="text-xs text-red-500">{errors.email.message}</span>
        )}
      </div>

      {rateLimitMessage ? (
        <div className={`text-sm p-3 rounded-md ${
          rateLimitMessage.includes("Email sent")
            ? "bg-green-50 text-green-700 border border-green-200"
            : "bg-yellow-50 text-yellow-700 border border-yellow-200"
        }`}>
          <div>{rateLimitMessage}</div>
          {countdown > 0 && (
            <div className="mt-2 text-xs opacity-75">
              You can try again in {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
            </div>
          )}
        </div>
      ) : (
        <p className="text-sm text-muted-foreground">
          Enter your email address and we&apos;ll send you a link to reset your password.
        </p>
      )}

      <Button
        type="submit"
        className="mt-1 w-full"
        disabled={isSubmitting || isRateLimited}
      >
        {isSubmitting ? "Sending..." : isRateLimited ? "Please Wait..." : "Send Reset Link"}
      </Button>

      <p className="text-sm text-black text-center">
        Remember your password?{" "}
        <Link
          href="/auth/login"
          className="font-medium text-black underline"
        >
          Back to login
        </Link>
      </p>
    </form>
  );
}
