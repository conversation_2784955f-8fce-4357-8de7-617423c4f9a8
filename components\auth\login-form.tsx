"use client";

import { useId, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoginFormData, LoginSchema } from "@/lib/schema";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Button } from "../ui/button";
import { toast } from "sonner";
import Link from "next/link";
import { useRoleRedirect } from "@/hooks/use-role-redirect";
import { signIn } from "@/lib/auth-client";


export default function LoginForm() {
   const {
    register,
    reset,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(LoginSchema),
    defaultValues: { email: "", password: "" },
  });

  const [isVisible, setIsVisible] = useState(false);


  const id = useId();
  const { redirectToRoleDashboard } = useRoleRedirect();
  const [_, setIsLoggingIn] = useState(false);

  const toggleVisibility = () => setIsVisible((prev) => !prev);

   const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoggingIn(true);

      // Use client-side auth for better session synchronization
      const result = await signIn.email({
        email: data.email,
        password: data.password,
        fetchOptions: {
          onRequest: () => {
            console.log('Login request started');
          },
          onResponse: () => {
            console.log('Login response received');
          },
          onError: (ctx) => {
            setIsLoggingIn(false);
            toast.error(ctx.error.message || "Login failed");
          },
          onSuccess: async () => {
            toast.success("Login successful");
            reset();

            // Update lastLoginAt after successful login
            try {
              await fetch('/api/auth/update-last-login', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
              });
            } catch (error) {
              console.error('Failed to update last login:', error);
            }

            // Wait a moment for the session to be established, then redirect
            setTimeout(() => {
              setIsLoggingIn(false);
              redirectToRoleDashboard();
            }, 100); // Increased timeout to 1 second
          },
        },
      });

      // If we get here without error, the login was successful
      if (result.error) {
        setIsLoggingIn(false);
        toast.error(result.error.message || "Login failed");
      }
    } catch (error) {
      setIsLoggingIn(false);
      console.error('Login error:', error);
      toast.error("Something went wrong during login");
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex w-full flex-col gap-4"
    >
      <div className="flex flex-col gap-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          {...register("email")}
        />
        {errors.email && (
          <span className="text-xs text-red-500">{errors.email.message}</span>
        )}
      </div>
      <div className="flex flex-col gap-2">
        <Label htmlFor={id}>Password</Label>
        <div className="relative">
          <Input
            id={id}
            type={isVisible ? "text" : "password"}
            placeholder="Password"
            autoComplete="current-password"
            className="pe-9"
            {...register("password")}
          />
          <button
            className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
            type="button"
            onClick={toggleVisibility}
            aria-label={isVisible ? "Hide password" : "Show password"}
            aria-pressed={isVisible}
            aria-controls="password"
          >
            {isVisible ? (
              <EyeOffIcon size={16} aria-hidden="true" />
            ) : (
              <EyeIcon size={16} aria-hidden="true" />
            )}
          </button>
        </div>
        {errors.password && (
          <span className="text-xs text-red-500">
            {errors.password.message}
          </span>
        )}
      </div>
      <p className="text-sm text-black text-right">
                Forgot your password?{" "}
                <Link
                  href="/auth/forgot-password"
                  className="font-medium text-black underline"
                >
                  Reset it here
                </Link>
              </p>
      <Button type="submit" className="mt-1 w-full" disabled={isSubmitting}>
        {isSubmitting ? "Logging in..." : "Login"}
      </Button>
    </form>
  )
}