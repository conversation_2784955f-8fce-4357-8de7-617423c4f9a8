
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Save, Eye, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { updateBlog } from "@/app/(dashboard)/admin/blog/actions";
import { UpdateBlogSchema, UpdateBlogFormData } from "@/lib/schema";
import RichTextEditor from "@/components/ui/rich-text-editor";
// import ImageUpload from "@/components/ui/image-upload";
import Link from "next/link";
// import { getBlogById } from "@/data/dal";

interface EditBlogFormProps {
  data: ({ 
    author: { name: string | null; id: string; email: string; }; 
  } & { 
    id: string; 
    image: string | null; 
    createdAt: Date; 
    updatedAt: Date; 
    title: string; 
    description: string; 
    content: string | null; 
    published: boolean; 
    authorId: string; 
  }) | null;
}

export default function EditBlogForm({ data }: EditBlogFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(true);
  const [content, setContent] = useState("");
  const [image, setImage] = useState("");
  const [blog, setBlog] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<UpdateBlogFormData>({
    resolver: zodResolver(UpdateBlogSchema),
    mode: "onSubmit", // Only validate on submit, not on change
    defaultValues: {
      id: "",
      title: "",
      description: "",
      content: "",
      image: "",
      published: false,
    },
  });

  // Initialize form with data when component mounts
  useEffect(() => {
    if (data) {
      setBlog(data);
      setContent(data.content || "");
      setImage(data.image || "");
      
      // Reset form with actual data
      reset({
        id: data.id,
        title: data.title,
        description: data.description,
        content: data.content || "",
        image: data.image || "",
        published: data.published,
      });
      
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [data, reset]);

  // Update form content when rich text editor changes
  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    setValue("content", newContent);
  };

  // Update image when it changes
//   const handleImageChange = (newImage: string) => {
//     setImage(newImage);
//     setValue("image", newImage);
//   };

  const published = watch("published");

  const onSubmit = async (formData: UpdateBlogFormData) => {
    startTransition(async () => {
      try {
        const result = await updateBlog({
          ...formData,
          content,
          image,
        });

        if (result.success) {
          toast.success(result.success.reason);
          router.push("/admin/blog");
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Update blog error:", error);
        toast.error("Failed to update blog post");
      }
    });
  };

  const handleSaveAsDraft = () => {
    setValue("published", false);
    // Use setTimeout to ensure the setValue has been processed
    setTimeout(() => {
      handleSubmit(onSubmit)();
    }, 0);
  };

  const handlePublish = () => {
    setValue("published", true);
    // Use setTimeout to ensure the setValue has been processed
    setTimeout(() => {
      handleSubmit(onSubmit)();
    }, 0);
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading blog post...</span>
        </div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Blog Post Not Found</h1>
          <Link href="/admin/blog">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Title */}
            <Card>
              <CardHeader>
                <CardTitle>Post Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    {...register("title")}
                    placeholder="Enter blog post title"
                    disabled={isPending}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Enter a brief description of your blog post"
                    rows={3}
                    disabled={isPending}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Content */}
            <Card>
              <CardHeader>
                <CardTitle>Content *</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextEditor
                  content={content}
                  onChange={handleContentChange}
                  placeholder="Start writing your blog post..."
                />
                {errors.content && (
                  <p className="text-sm text-red-600 mt-1">{errors.content.message}</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="published"
                    checked={published}
                    onCheckedChange={(checked) => setValue("published", !!checked)}
                    disabled={isPending}
                  />
                  <Label htmlFor="published">Published</Label>
                </div>

                <div className="flex flex-col space-y-2">
                  <Button
                    type="button"
                    onClick={handleSaveAsDraft}
                    variant="outline"
                    disabled={isPending}
                    className="w-full"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isPending ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button
                    type="button"
                    onClick={handlePublish}
                    disabled={isPending}
                    className="w-full"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    {isPending ? "Publishing..." : "Publish Now"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Featured Image */}
            {/* <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="image">Image URL</Label>
                  <Input
                    id="image"
                    value={image}
                    onChange={(e) => handleImageChange(e.target.value)}
                    placeholder="Enter image URL"
                    disabled={isPending}
                  />
                  {image && (
                    <div className="mt-2">
                      <img
                        src={image}
                        alt="Featured image preview"
                        className="w-full h-32 object-cover rounded-md border"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
      
                <ImageUpload
                  value={image}
                  onChange={handleImageChange}
                  onRemove={() => handleImageChange("")}
                  disabled={isPending}
                />
              </CardContent>
            </Card> */}

            {/* Post Info */}
            <Card>
              <CardHeader>
                <CardTitle>Post Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-gray-600">
                <div>
                  <strong>Created:</strong> {new Date(blog.createdAt).toLocaleDateString()}
                </div>
                <div>
                  <strong>Updated:</strong> {new Date(blog.updatedAt).toLocaleDateString()}
                </div>
                <div>
                  <strong>Author:</strong> {blog.author.name || blog.author.email}
                </div>
                <div>
                  <strong>Status:</strong> 
                  <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
                    blog.published 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {blog.published ? 'Published' : 'Draft'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
  );
}