import { getBlogs } from "@/data/dal";
import BlogTable from "./blog-table";

interface BlogWrapperProps {
  page: number;
  search: string;
  published: string;
}

export default async function BlogWrapper({ page, search, published }: BlogWrapperProps) {
  const blogsData = await getBlogs({ page, search, published });
  const blogs = blogsData?.blogs ?? [];
  const pagination = blogsData?.pagination ?? {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  return (
    <BlogTable 
      blogs={blogs} 
      pagination={pagination}
      searchTerm={search}
      publishedFilter={published}
    />
  );
}
