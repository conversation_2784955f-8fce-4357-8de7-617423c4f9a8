"use client";

import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Droplets,
  AlertTriangle,
  Facebook,
  Twitter,
  Linkedin,
  Youtube,
} from "lucide-react";

export default function CareersPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">ASAD</span>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              HOME
            </Link>
            <Link href="/company" className="text-gray-600 hover:text-blue-600">
              COMPANY
            </Link>
            <Link
              href="/products"
              className="text-gray-600 hover:text-blue-600"
            >
              PRODUCTS
            </Link>
            <Link
              href="/services"
              className="text-gray-600 hover:text-blue-600"
            >
              SERVICES
            </Link>
            <Link
              href="/governance"
              className="text-gray-600 hover:text-blue-600"
            >
              GOVERNANCE
            </Link>
            <Link href="/careers" className="text-blue-600 font-medium">
              CAREERS
            </Link>
            <Link href="/media" className="text-gray-600 hover:text-blue-600">
              MEDIA
            </Link>
            <Link href="/contact" className="text-gray-600 hover:text-blue-600">
              CONTACT
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button className="bg-black text-white hover:bg-gray-800">
              BE A DISTRIBUTOR
            </Button>
            <Button variant="outline">LOGIN</Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative h-96 bg-gray-100 overflow-hidden">
        <Image
          src="/placeholder.svg?height=400&width=800"
          alt="Industrial worker in safety gear"
          width={800}
          height={400}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-4">
            <div className="max-w-lg">
              <h1 className="text-5xl font-bold text-white mb-4">
                HELP US KEEP
                <br />
                <span className="bg-red-600 px-4 py-2 inline-block">
                  THE MOVEMENT
                </span>
              </h1>
            </div>
          </div>
        </div>
      </section>

      {/* Recruitment Scam Warning */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <Alert className="max-w-4xl mx-auto bg-yellow-50 border-yellow-200">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-center">
              <strong className="text-yellow-800">
                BEWARE OF RECRUITMENT SCAMS!
              </strong>
              <br />
              <span className="text-gray-700">
                ASAD Lubricants will never ask you to pay for a job application
              </span>
            </AlertDescription>
          </Alert>

          {/* Job Search */}
          <div className="max-w-2xl mx-auto mt-12">
            <div className="flex gap-4">
              <Select>
                <SelectTrigger className="flex-1 bg-white border-gray-300">
                  <SelectValue placeholder="SELECT SPECIALIZATION" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="engineering">Engineering</SelectItem>
                  <SelectItem value="sales">Sales & Marketing</SelectItem>
                  <SelectItem value="operations">Operations</SelectItem>
                  <SelectItem value="finance">Finance & Accounting</SelectItem>
                  <SelectItem value="hr">Human Resources</SelectItem>
                  <SelectItem value="logistics">Logistics</SelectItem>
                </SelectContent>
              </Select>
              <Button className="bg-black text-white hover:bg-gray-800 px-8">
                SEARCH JOBS
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Mission Statement */}
      <section className="py-20 bg-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold max-w-5xl mx-auto leading-tight">
            AT ASAD LUBRICANTS, WE DON&apos;T JUST FUEL MACHINES; WE EMPOWER
            PEOPLE. WHETHER YOU&apos;RE ON THE FIELD, IN THE LAB, OR BEHIND THE
            SCENES, YOUR IDEAS AND ENERGY HELP KEEP THE WORLD MOVING.
          </h2>
        </div>
      </section>

      {/* Everything We Offer You */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              EVERYTHING
              <br />
              <span className="bg-blue-500 text-white px-4 py-2 inline-block">
                WE OFFER YOU
              </span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="bg-white border-none shadow-lg">
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 text-sm mb-4">01</div>
                <h3 className="text-2xl font-bold text-gray-600 mb-4">
                  REAL
                  <br />
                  IMPACT
                </h3>
              </CardContent>
            </Card>

            <Card className="bg-white border-none shadow-lg">
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 text-sm mb-4">02</div>
                <h3 className="text-2xl font-bold text-gray-600 mb-4">
                  ROOM TO
                  <br />
                  GROW
                </h3>
              </CardContent>
            </Card>

            <Card className="bg-white border-none shadow-lg">
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 text-sm mb-4">03</div>
                <h3 className="text-2xl font-bold text-gray-600 mb-4">
                  STRONG
                  <br />
                  CULTURE
                </h3>
              </CardContent>
            </Card>

            <Card className="bg-white border-none shadow-lg">
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 text-sm mb-4">04</div>
                <h3 className="text-2xl font-bold text-gray-600 mb-4">
                  MEANINGFUL
                  <br />
                  MISSION
                </h3>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-blue-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">JOIN OUR MAILING LIST</h2>
            <p className="text-blue-200 mb-8">
              Subscribe to our newsletter and unlock a world of exclusive
              benefits. Be the first to know about our latest products, special
              promotions, and exciting updates.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter Your Email"
                className="bg-blue-800 border-blue-700 text-white placeholder-blue-300"
              />
              <Button className="bg-yellow-500 text-black hover:bg-yellow-600">
                SUBSCRIBE NOW
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-6 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-4">COMPANY</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">PRODUCTS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">SERVICES</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">GOVERNANCE</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CAREERS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CONTACT</h3>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2025 All rights reserved</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Terms of Service</span>
              <span className="text-gray-400 text-sm">Privacy Policy</span>
              <div className="flex space-x-4">
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Facebook className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Twitter className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Linkedin className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Youtube className="w-5 h-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
