/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, Eye, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { updateCareer } from "@/app/(dashboard)/admin/careers/actions";
import { UpdateCareerSchema, UpdateCareerFormData } from "@/lib/schema";
import RichTextEditor from "@/components/ui/rich-text-editor";
import Link from "next/link";

const jobTypes = [
  "Full-time",
  "Part-time",
  "Contract",
  "Temporary",
  "Internship",
  "Remote",
  "Hybrid",
];

interface EditCareerFormProps {
 data: ({ _count: { applications: number; }; } & { type: string | null; id: string; title: string; description: string; requirements: string; location: string | null; salary: string | null; active: boolean; createdAt: Date; updatedAt: Date; postedDate: Date; }) | null;
}

export default function EditCareerForm({ data }: EditCareerFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(true);
  const [description, setDescription] = useState("");
  const [requirements, setRequirements] = useState("");
  const [career, setCareer] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<UpdateCareerFormData>({
    resolver: zodResolver(UpdateCareerSchema),
    mode: "onSubmit", // Only validate on submit, not on change
    defaultValues: {
      id: "",
      title: "",
      description: "",
      requirements: "",
      location: "",
      type: "",
      salary: "",
      active: true,
    },
  });

  const active = watch("active");

  // Update form content when rich text editor changes
  const handleDescriptionChange = (newContent: string) => {
    setDescription(newContent);
    setValue("description", newContent);
  };

  const handleRequirementsChange = (newContent: string) => {
    setRequirements(newContent);
    setValue("requirements", newContent);
  };

  // Load career data
  useEffect(() => {
    if (data) {
      setCareer(data);
      setDescription(data.description || "");
      setRequirements(data.requirements || ""); 
      
      // Reset form with actual data
      reset({
        id: data.id,
        title: data.title,
        description: data.description,
        requirements: data.requirements || "",
        location: data.location || "",
        type: data.type || "",
        salary: data.salary || "",
        active: data.active,
      });
      
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [data, reset]);

  const onSubmit = async (data: UpdateCareerFormData) => {
    startTransition(async () => {
      try {
        const result = await updateCareer({
          ...data,
          description,
          requirements,
        });

        if (result.success) {
          toast.success(result.success.reason);
          router.push("/admin/careers");
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Update career error:", error);
        toast.error("Failed to update career posting");
      }
    });
  };

  const handleSaveAsDraft = () => {
    setValue("active", false);
    handleSubmit(onSubmit)();
  };

  const handlePublish = () => {
    setValue("active", true);
    handleSubmit(onSubmit)();
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading career posting...</span>
        </div>
      </div>
    );
  }

  if (!career) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Career Posting Not Found</h1>
          <Link href="/admin/careers">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Careers
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Job Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    {...register("title")}
                    placeholder="Enter job title"
                    disabled={isPending}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      {...register("location")}
                      placeholder="e.g., New York, NY or Remote"
                      disabled={isPending}
                    />
                    {errors.location && (
                      <p className="text-sm text-red-600 mt-1">{errors.location.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">Job Type</Label>
                    <Select 
                      value={watch("type") || ""} 
                      onValueChange={(value) => setValue("type", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select job type" />
                      </SelectTrigger>
                      <SelectContent>
                        {jobTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className="text-sm text-red-600 mt-1">{errors.type.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="salary">Salary Range</Label>
                  <Input
                    id="salary"
                    {...register("salary")}
                    placeholder="e.g., $70,000 - $90,000 or Competitive"
                    disabled={isPending}
                  />
                  {errors.salary && (
                    <p className="text-sm text-red-600 mt-1">{errors.salary.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Job Description */}
            <Card>
              <CardHeader>
                <CardTitle>Job Description *</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextEditor
                  content={description}
                  onChange={handleDescriptionChange}
                  placeholder="Describe the role, responsibilities, and what makes this position exciting..."
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                )}
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card>
              <CardHeader>
                <CardTitle>Requirements *</CardTitle>
              </CardHeader>
              <CardContent>
                <RichTextEditor
                  content={requirements}
                  onChange={handleRequirementsChange}
                  placeholder="List the required skills, experience, education, and qualifications..."
                />
                {errors.requirements && (
                  <p className="text-sm text-red-600 mt-1">{errors.requirements.message}</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="active"
                    checked={active}
                    onCheckedChange={(checked) => setValue("active", !!checked)}
                    disabled={isPending}
                  />
                  <Label htmlFor="active">Active</Label>
                </div>

                <div className="flex flex-col space-y-2">
                  <Button
                    type="button"
                    onClick={handleSaveAsDraft}
                    variant="outline"
                    disabled={isPending}
                    className="w-full"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isPending ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button
                    type="button"
                    onClick={handlePublish}
                    disabled={isPending}
                    className="w-full"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    {isPending ? "Publishing..." : "Publish Now"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Job Info */}
            <Card>
              <CardHeader>
                <CardTitle>Job Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-gray-600">
                <div>
                  <strong>Posted:</strong> {new Date(career.postedDate).toLocaleDateString()}
                </div>
                <div>
                  <strong>Created:</strong> {new Date(career.createdAt).toLocaleDateString()}
                </div>
                <div>
                  <strong>Updated:</strong> {new Date(career.updatedAt).toLocaleDateString()}
                </div>
                <div>
                  <strong>Applications:</strong> {career._count?.applications || 0}
                </div>
              </CardContent>
            </Card>

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle>Tips for Great Job Postings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm text-gray-600">
                <div>
                  <strong>Clear Title:</strong> Use specific, searchable job titles
                </div>
                <div>
                  <strong>Detailed Description:</strong> Include day-to-day responsibilities
                </div>
                <div>
                  <strong>Specific Requirements:</strong> List must-have vs. nice-to-have skills
                </div>
                <div>
                  <strong>Company Culture:</strong> Highlight what makes your team special
                </div>
                <div>
                  <strong>Growth Opportunities:</strong> Mention career development paths
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
  );
}
