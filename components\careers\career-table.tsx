"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  MapPin,
  Calendar,
  Filter,
  Eye,
  EyeOff,
  Users,
  DollarSign,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState, useTransition, useCallback, useEffect } from "react";
import { toast } from "sonner";
import { deleteCareer, toggleCareerActive } from "@/app/(dashboard)/admin/careers/actions";
import { useRouter, useSearchPara<PERSON>, usePathname } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  // AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import Link from "next/link";

type PrismaCareer = {
  id: string;
  title: string;
  description: string;
  requirements: string;
  location: string | null;
  type: string | null;
  salary: string | null;
  active: boolean;
  postedDate: Date;
  createdAt: Date;
  updatedAt: Date;
  _count: {
    applications: number;
  };
};

type PaginationData = {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

interface CareerTableProps {
  careers: PrismaCareer[];
  pagination: PaginationData;
  searchTerm: string;
  activeFilter: string;
}

export default function CareerTable({ 
  careers, 
  pagination, 
  searchTerm: initialSearchTerm, 
  activeFilter: initialActiveFilter 
}: CareerTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // Dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Form states
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [activeFilter, setActiveFilter] = useState(initialActiveFilter);
  const [careerToDelete, setCareerToDelete] = useState<PrismaCareer | null>(null);

  // Update URL with search params
  const updateSearchParams = useCallback((params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });
    
    // Reset to page 1 when searching or filtering
    if (params.search !== undefined || params.active !== undefined) {
      newSearchParams.set('page', '1');
    }
    
    router.push(`${pathname}?${newSearchParams.toString()}`);
  }, [router, pathname, searchParams]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== initialSearchTerm) {
        updateSearchParams({ search: searchTerm, active: activeFilter });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, activeFilter, initialSearchTerm, updateSearchParams]);

  // Handle active filter change
  const handleActiveFilterChange = (value: string) => {
    setActiveFilter(value);
    updateSearchParams({ search: searchTerm, active: value });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateSearchParams({ 
      search: searchTerm, 
      active: activeFilter, 
      page: page.toString() 
    });
  };

  // Delete career handlers
  const openDeleteDialog = (career: PrismaCareer) => {
    setCareerToDelete(career);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteCareer = async () => {
    if (!careerToDelete) return;
    
    startTransition(async () => {
      try {
        const result = await deleteCareer({ id: careerToDelete.id });
        if (result.success) {
          toast.success(result.success.reason);
          setIsDeleteDialogOpen(false);
          setCareerToDelete(null);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Delete career error:", error);
        toast.error("Failed to delete career posting");
      }
    });
  };

  // Toggle active status
  const handleToggleActive = async (career: PrismaCareer) => {
    startTransition(async () => {
      try {
        const result = await toggleCareerActive(career.id);
        if (result.success) {
          toast.success(result.success.reason);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Toggle career active error:", error);
        toast.error("Failed to update career status");
      }
    });
  };

  // Format date helper
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <div>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Career Postings</CardTitle>
          <Link href="/admin/careers/create">
            <Button disabled={isPending}>
              <Plus className="w-4 h-4 mr-2" />
              Post New Job
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {/* Search and Filter */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search career postings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={activeFilter} onValueChange={handleActiveFilterChange}>
              <SelectTrigger className="w-32">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Jobs</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Careers Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Job Title</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Applications</TableHead>
                <TableHead>Posted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {careers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No career postings found
                  </TableCell>
                </TableRow>
              ) : (
                careers.map((career) => (
                  <TableRow key={career.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{career.title}</p>
                        <div className="flex items-center text-sm text-gray-600 mt-1">
                          {career.salary && (
                            <div className="flex items-center mr-4">
                              <DollarSign className="w-3 h-3 mr-1" />
                              <span>{career.salary}</span>
                            </div>
                          )}
                          {career.type && (
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {career.type}
                            </span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{career.location || "Not specified"}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {career.type || "Not specified"}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={career.active ? "default" : "secondary"}
                      >
                        {career.active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{career._count.applications}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{formatDate(career.postedDate)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleActive(career)}
                          disabled={isPending}
                          title={career.active ? "Deactivate" : "Activate"}
                        >
                          {career.active ? (
                            <EyeOff className="w-4 h-4" />
                          ) : (
                            <Eye className="w-4 h-4" />
                          )}
                        </Button>
                        <Link href={`/admin/careers/${career.id}/edit`}>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={isPending}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openDeleteDialog(career)}
                          disabled={isPending}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.currentPage - 1) * 10) + 1} to{" "}
                {Math.min(pagination.currentPage * 10, pagination.totalCount)} of{" "}
                {pagination.totalCount} career postings
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious size="sm"
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (pagination.hasPreviousPage) {
                          handlePageChange(pagination.currentPage - 1);
                        }
                      }}
                      className={!pagination.hasPreviousPage ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNumber = Math.max(1, pagination.currentPage - 2) + i;
                    if (pageNumber > pagination.totalPages) return null;
                    
                    return (
                      <PaginationItem key={pageNumber}>
                        <PaginationLink size="sm"
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(pageNumber);
                          }}
                          isActive={pageNumber === pagination.currentPage}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}
                  
                  <PaginationItem>
                    <PaginationNext size="sm"
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (pagination.hasNextPage) {
                          handlePageChange(pagination.currentPage + 1);
                        }
                      }}
                      className={!pagination.hasNextPage ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the career posting{" "}
              <strong>&quot;{careerToDelete?.title}&quot;</strong> and remove all associated data.
              {careerToDelete && careerToDelete._count.applications > 0 && (
                <span className="block mt-2 text-red-600 font-medium">
                  Warning: This posting has {careerToDelete._count.applications} application(s).
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCareer}
              disabled={isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isPending ? "Deleting..." : "Delete Career"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
