import { getCareers } from "@/data/dal";
import CareerTable from "./career-table";

interface CareerWrapperProps {
  page: number;
  search: string;
  active: string;
}

export default async function CareerWrapper({ page, search, active }: CareerWrapperProps) {
  const careersData = await getCareers({ page, search, active });
  const careers = careersData?.careers ?? [];
  const pagination = careersData?.pagination ?? {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  return (
    <CareerTable 
      careers={careers} 
      pagination={pagination}
      searchTerm={search}
      activeFilter={active}
    />
  );
}
