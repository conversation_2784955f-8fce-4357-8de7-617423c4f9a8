"use client";

import type React from "react";

import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Droplets,
  MapPin,
  Phone,
  Users,
  Facebook,
  Twitter,
  Linkedin,
  Youtube,
} from "lucide-react";
import { useState } from "react";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    orderNumber: "",
    message: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Contact form submitted:", formData);
    // Handle form submission
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">ASAD</span>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              HOME
            </Link>
            <Link href="/company" className="text-gray-600 hover:text-blue-600">
              COMPANY
            </Link>
            <Link
              href="/products"
              className="text-gray-600 hover:text-blue-600"
            >
              PRODUCTS
            </Link>
            <Link
              href="/services"
              className="text-gray-600 hover:text-blue-600"
            >
              SERVICES
            </Link>
            <Link
              href="/governance"
              className="text-gray-600 hover:text-blue-600"
            >
              GOVERNANCE
            </Link>
            <Link href="/careers" className="text-gray-600 hover:text-blue-600">
              CAREERS
            </Link>
            <Link href="/media" className="text-gray-600 hover:text-blue-600">
              MEDIA
            </Link>
            <Link href="/contact" className="text-blue-600 font-medium">
              CONTACT
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button className="bg-black text-white hover:bg-gray-800">
              BE A DISTRIBUTOR
            </Button>
            <Button variant="outline">LOGIN</Button>
          </div>
        </div>
      </header>

      {/* Hero Section with Contact Form */}
      <section className="relative min-h-screen bg-gray-100 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/placeholder.svg?height=800&width=1200"
            alt="Industrial Truck"
            width={1200}
            height={800}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Left Side - Title */}
            <div className="pt-20">
              <h1 className="text-6xl font-bold text-white mb-8">
                GET IN
                <br />
                <span className="bg-blue-500 px-4 py-2 inline-block transform -rotate-1">
                  TOUCH
                </span>
              </h1>
            </div>

            {/* Right Side - Contact Form */}
            <Card className="bg-white rounded-3xl shadow-2xl">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                  WE&apos;D LOVE TO HEAR FROM YOU
                </h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        placeholder="NAME"
                        value={formData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        className="bg-gray-100 border-none rounded-lg py-6"
                        required
                        aria-label="Your Name"
                      />
                    </div>
                    <div>
                      <Input
                        type="email"
                        placeholder="EMAIL ADDRESS"
                        value={formData.email}
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        className="bg-gray-100 border-none rounded-lg py-6"
                        required
                        aria-label="Your Email Address"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Select
                        onValueChange={(value) =>
                          handleInputChange("subject", value)
                        }
                      >
                        <SelectTrigger className="bg-gray-100 border-none rounded-lg py-6">
                          <SelectValue placeholder="SUBJECT" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">
                            General Inquiry
                          </SelectItem>
                          <SelectItem value="products">
                            Product Information
                          </SelectItem>
                          <SelectItem value="distributor">
                            Distributor Inquiry
                          </SelectItem>
                          <SelectItem value="support">
                            Technical Support
                          </SelectItem>
                          <SelectItem value="careers">
                            Career Opportunities
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Input
                        placeholder="ORDER NUMBER (OPTIONAL)"
                        value={formData.orderNumber}
                        onChange={(e) =>
                          handleInputChange("orderNumber", e.target.value)
                        }
                        className="bg-gray-100 border-none rounded-lg py-6"
                        aria-label="Order Number (Optional)"
                      />
                    </div>
                  </div>

                  <div>
                    <Textarea
                      placeholder="MESSAGE"
                      value={formData.message}
                      onChange={(e) =>
                        handleInputChange("message", e.target.value)
                      }
                      className="bg-gray-100 border-none rounded-lg min-h-[120px] resize-none"
                      required
                      aria-label="Your Message"
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-black text-white hover:bg-gray-800 py-6 rounded-full text-lg font-medium"
                  >
                    SUBMIT
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-12 text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <p className="text-gray-600 font-medium mb-2">
                  12 Energy Drive, Industrial Zone
                </p>
                <p className="text-gray-600">Apapa, Lagos, Nigeria</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Phone className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <p className="text-gray-800 font-medium mb-2">
                  +234 ************
                </p>
                <p className="text-gray-600"><EMAIL></p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <p className="text-gray-800 font-medium mb-2">
                  Distributors across
                </p>
                <p className="text-gray-600">all 36 states</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-blue-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">JOIN OUR MAILING LIST</h2>
            <p className="text-blue-200 mb-8">
              Subscribe to our newsletter and unlock a world of exclusive
              benefits. Be the first to know about our latest products, special
              promotions, and exciting updates.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter Your Email"
                className="bg-blue-800 border-blue-700 text-white placeholder-blue-300"
              />
              <Button className="bg-gray-800 text-white hover:bg-gray-700">
                SUBSCRIBE NOW
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-6 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-4">COMPANY</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">PRODUCTS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">SERVICES</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">GOVERNANCE</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CAREERS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CONTACT</h3>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2025 All rights reserved</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Terms of Service</span>
              <span className="text-gray-400 text-sm">Privacy Policy</span>
              <div className="flex space-x-4">
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Facebook className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Twitter className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Linkedin className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Youtube className="w-5 h-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
