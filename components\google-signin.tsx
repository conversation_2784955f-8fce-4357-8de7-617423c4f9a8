"use client";
import { signIn } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { GoogleIcon } from "./ui/icons";
import { useRouter } from "next/navigation";

export default function GoogleSignin() {
  const [isPending, setIsPending] = useState(false);
  const router = useRouter();

  async function handleClick() {
    setIsPending(true);

    try {
      await signIn.social({
        provider: "google",
        callbackURL: "/admin/dashboard", // Will be handled by middleware based on role
        errorCallbackURL: "/auth/login?error=google_signin_failed",
        fetchOptions: {
          onSuccess: () => {
            // Let middleware handle the redirect based on user role
            router.refresh();
          },
          onError: (ctx) => {
            console.error('Google sign-in error:', ctx.error);
            setIsPending(false);
          },
        },
      });
    } catch (error) {
      console.error('Google sign-in error:', error);
      setIsPending(false);
    }
  }

  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center"
      type="button"
      onClick={handleClick}
      disabled={isPending}
    >
      <GoogleIcon className="mr-2" />
      Google
    </Button>
  );
}
