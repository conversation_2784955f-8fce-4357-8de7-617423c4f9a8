"use client";
import { signIn, useSession } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { GoogleIcon } from "./ui/icons";

export default function GoogleSignin() {
  const [isPending, setIsPending] = useState(false);
  const {data:session} = useSession();

  async function handleClick() {
    setIsPending(true);

    try {
      await signIn.social({
        provider: "google",
        callbackURL: session?.user.role === "ADMIN" ? "/admin/dashboard" : "/",
        errorCallbackURL: "/auth/login/error",
        fetchOptions: {
          onSuccess: async () => {
            // Update lastLoginAt after successful Google sign-in
            try {
              await fetch('/api/auth/update-last-login', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
              });
            } catch (error) {
              console.error('Failed to update last login:', error);
            }
          },
        },
      });
    } catch (error) {
      console.error('Google sign-in error:', error);
    }

    setIsPending(false);
  }

  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center"
      type="button"
      onClick={handleClick}
      disabled={isPending}
    >
      <GoogleIcon className="mr-2" />
      Google
    </Button>
  );
}
