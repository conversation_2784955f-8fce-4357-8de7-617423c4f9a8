"use client";
import { signIn } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { GoogleIcon } from "./ui/icons";

export default function GoogleSignin() {
  const [isPending, setIsPending] = useState(false);

  async function handleClick() {
    setIsPending(true);

    try {
      await signIn.social({
        provider: "google",
        callbackURL: "/",
        errorCallbackURL: "/auth/login?error=google_signin_failed",
        fetchOptions: {
          onSuccess: async () => {
            // Update lastLoginAt after successful Google sign-in
            try {
              const response = await fetch('/api/auth/update-last-login', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                credentials: 'include', // Important for session cookies
              });

              if (response.ok) {
                console.log('Last login time updated successfully');
              } else {
                console.warn('Failed to update last login time:', response.status);
              }
            } catch (error) {
              console.error('Failed to update last login:', error);
            }

            // Redirect will be handled by middleware
            setIsPending(false);
          },
          onError: (ctx) => {
            console.error('Google sign-in error:', ctx.error);
            setIsPending(false);
          },
        },
      });
    } catch (error) {
      console.error('Google sign-in error:', error);
      setIsPending(false);
    }
  }

  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center"
      type="button"
      onClick={handleClick}
      disabled={isPending}
    >
      <GoogleIcon className="mr-2" />
      Google
    </Button>
  );
}
