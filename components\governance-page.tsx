import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Droplets,
  Download,
  Facebook,
  Twitter,
  Linkedin,
  Youtube,
} from "lucide-react";

export default function GovernancePage() {
  const policies = [
    { name: "GIFT POLICY", icon: Download },
    { name: "SHARE TRADING POLICY", icon: Download },
    { name: "WHISTLEBLOWING POLICY", icon: Download },
    { name: "ANTI CORRUPTION POLICY", icon: Download },
    { name: "BUSINESS ETHICS POLICY", icon: Download },
    { name: "ASAD TRADING POLICY", icon: Download },
  ];

  const hsePolicies = [
    { name: "HSE POLICY", icon: Download },
    { name: "QUALITY POLICY", icon: Download },
    { name: "ISO 9001 CERTIFICATE", icon: Download },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">ASAD</span>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              HOME
            </Link>
            <Link href="/company" className="text-gray-600 hover:text-blue-600">
              COMPANY
            </Link>
            <Link
              href="/products"
              className="text-gray-600 hover:text-blue-600"
            >
              PRODUCTS
            </Link>
            <Link
              href="/services"
              className="text-gray-600 hover:text-blue-600"
            >
              SERVICES
            </Link>
            <Link href="/governance" className="text-blue-600 font-medium">
              GOVERNANCE
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              CAREERS
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              MEDIA
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              CONTACT
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button className="bg-black text-white hover:bg-gray-800">
              BE A DISTRIBUTOR
            </Button>
            <Button variant="outline">LOGIN</Button>
          </div>
        </div>
      </header>

      {/* Corporate Governance Header */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-6">
            OUR CORPORATE GOVERNANCE
          </h1>
          <p className="text-gray-600 text-lg max-w-3xl mx-auto">
            At ASAD Lubricants, our commitment to integrity, transparency, and
            accountability forms every part of our business. From internal
            controls to external audits, we uphold the highest standards to
            ensure sustainable growth and stakeholder confidence.
          </p>
        </div>
      </section>

      {/* Policy Downloads */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-6">
            {policies.map((policy, index) => (
              <Card
                key={index}
                className="bg-gray-100 hover:bg-gray-200 transition-colors cursor-pointer"
              >
                <CardContent className="p-6 flex items-center justify-between">
                  <span className="font-medium text-gray-800">
                    {policy.name}
                  </span>
                  <Download className="w-5 h-5 text-gray-600" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Compliance Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-800 mb-12">COMPLIANCE</h2>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Internal Control System */}
            <Card className="bg-blue-500 text-white">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-6">
                  INTERNAL CONTROL SYSTEM
                </h3>
                <p className="text-blue-100 mb-6">
                  Our internal control system ensures all business processes are
                  safeguarded, documented, monitored and the scope of the
                  Company. The system of internal controls is to provide
                  reasonable assurance against material misstatement, prevent
                  and detect fraud and other irregularities, ensure accuracy and
                  completeness of the accounting records, and timely preparation
                  of reliable financial information.
                </p>
                <p className="text-blue-100 mb-6">
                  There is an effective internal control function within the
                  Company which gives reasonable assurance against the material
                  misstatement or loss and includes the maintenance of proper
                  accounting records for ensuring reliable financial reporting.
                </p>
                <p className="text-blue-100 mb-8">
                  Our internal audit is supported by external auditors from and
                  auditors are appointed for the annual audit of the Company.
                </p>

                <div className="flex gap-4">
                  <Button
                    variant="outline"
                    className="text-white border-white hover:bg-white hover:text-blue-500 bg-transparent"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    FRAUD POLICY
                  </Button>
                  <Button
                    variant="outline"
                    className="text-white border-white hover:bg-white hover:text-blue-500 bg-transparent"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    WHISTLEBLOWING POLICY
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Risk Management */}
            <div>
              <h3 className="text-2xl font-bold text-gray-800 mb-6">
                RISK MANAGEMENT
              </h3>
              <p className="text-gray-600 mb-6">
                ASAD Lubricants complies with all legal requirements, internal
                policies, tax laws, and regulations. Our goal is resilience and
                resilience against potential threats to our people, assets,
                operations, and operations. Our goal is resilience and
                resilience against potential threats to our people, assets,
                operations, and operations.
              </p>
              <p className="text-gray-600 mb-6">
                ASAD Lubricants risk management framework is integrated into its
                daily business operations. The Company has established a
                comprehensive risk management framework that enables the
                identification, assessment, monitoring, and mitigation of risks
                across all business functions within the Company.
              </p>
              <p className="text-gray-600 mb-8">
                Our risk management includes strategic, financial, operational
                and operational risks. Our approach to risk management is to
                identify the key risks, assess their potential impact and
                likelihood, and develop appropriate risk mitigation strategies.
                The Board and its committees are actively involved in the
                oversight of risk management and the Company&apos;s overall risk
                profile against related developments, board and other
                responsibilities.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* HSE Policy Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-8">HSE POLICY</h2>
          <p className="text-gray-600 text-lg max-w-3xl mx-auto mb-16">
            At ASAD Lubricants, safety isn&apos;t just a standard — it&apos;s
            the foundation of everything we do. From our operations and
            workforce to our communities and host communities, protecting lives
            and the environment is our top priority.
          </p>

          {/* HSE Image */}
          <div className="mb-16">
            <Image
              src="/placeholder.svg?height=300&width=800"
              alt="Workers in safety gear"
              width={800}
              height={300}
              className="w-full max-w-4xl mx-auto rounded-lg"
            />
          </div>

          {/* HSE Pillars */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                SAFETY CULTURE & PEOPLE FIRST
              </h3>
              <p className="text-gray-600">
                Our commitment to safeguard stakeholders&apos; investment and
                the assets of the Company. The interest of internal controls is
                to...
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                INTERNATIONAL STANDARDS
              </h3>
              <p className="text-gray-600">
                Our commitment to safeguard stakeholders&apos; investment and
                the assets of the Company. The interest of internal controls is
                to...
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                SAFE, EFFICIENT SUPPLY CHAIN
              </h3>
              <p className="text-gray-600">
                Our commitment to safeguard stakeholders&apos; investment and
                the assets of the Company. The interest of internal controls is
                to...
              </p>
            </div>
          </div>

          {/* HSE Policy Downloads */}
          <div className="grid md:grid-cols-3 gap-6">
            {hsePolicies.map((policy, index) => (
              <Card
                key={index}
                className="bg-gray-100 hover:bg-gray-200 transition-colors cursor-pointer"
              >
                <CardContent className="p-6 flex items-center justify-between">
                  <span className="font-medium text-gray-800">
                    {policy.name}
                  </span>
                  <Download className="w-5 h-5 text-gray-600" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Corporate Social Responsibility */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-8">
            CORPORATE SOCIAL RESPONSIBILITY
          </h2>
          <p className="text-gray-600 text-lg max-w-4xl mx-auto mb-16">
            At ASAD PLC, the value of &apos;Responsibility&apos; is reflected
            and finds a natural place in our key drivers in the conduct of its
            business operations and its relationship with every stakeholder in
            the energy value chain. It is on this premise that we have made a
            commitment to the development of host communities in the areas of
            operation.
          </p>

          {/* CSR Images */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=250&width=350"
                alt="Community outreach"
                width={350}
                height={250}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=250&width=350"
                alt="Educational support"
                width={350}
                height={250}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=250&width=350"
                alt="Healthcare support"
                width={350}
                height={250}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-blue-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">JOIN OUR MAILING LIST</h2>
            <p className="text-blue-200 mb-8">
              Subscribe to our newsletter and unlock a world of exclusive
              benefits. Be the first to know about our latest products, special
              promotions, and exciting updates.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter Your Email"
                className="bg-white text-gray-800"
              />
              <Button className="bg-yellow-500 text-black hover:bg-yellow-600">
                SUBSCRIBE NOW
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-6 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-4">COMPANY</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">PRODUCTS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">SERVICES</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">GOVERNANCE</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CAREERS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CONTACT</h3>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2025 All rights reserved</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Terms of Service</span>
              <span className="text-gray-400 text-sm">Privacy Policy</span>
              <div className="flex space-x-4">
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Facebook className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Twitter className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Linkedin className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Youtube className="w-5 h-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
