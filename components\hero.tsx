import Image from "next/image";

export default function Hero() {
  const images = [
    { name: "Product1", src: "/heroImage1.png" },
    { name: "Product2", src: "/heroImage2.png" },
    { name: "Product3", src: "/heroImage3.png" },
    { name: "Product4", src: "/heroImage4.png" },
  ];

  return (
    <section className="min-h-screen bg-gradient-to-br from-[#B8D4FF] via-[#A3C7FE] to-[#8BB8FF] pt-16 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 h-full relative">
        <div className="grid lg:grid-cols-3 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-semibold text-black leading-tight whitespace-nowrap">
              DRIVING EXCELLENCE IN <br />
              <span className="bg-[#1794E5] text-white px-4 py-2 inline-block transform -rotate-2 shadow-lg whitespace-nowrap rounded-sm">
                LUBRICATION SOLUTIONS
              </span>
            </h1>
            <p className="hidden text-gray-600 text-lg mb-8">
              Premium Lubricants for Automotive, Industrial, Marine & Power
              Applications
            </p>
          </div>
          <div className="relative">
            <div className="absolute -bottom-[511px] left-1/2 transform -translate-x-1/2 z-30">
              <div className="flex justify-center space-x-2 lg:space-x-3">
                {images.map((image, i) => (
                  <div
                    key={i}
                    className="w-16 h-16 lg:w-20 lg:h-20 rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-200"
                  >
                    <Image
                      src={image.src}
                      alt={`Product ${i + 1}`}
                      width={239}
                      height={321}
                      className="object-contain"
                    />
                  </div>
                ))}
              </div>
            </div>
            <div className="absolute top-1/2 right-0 transform translate-y-1/2 h-full w-1/2 flex items-center justify-center">
              <div className="relative">
                <Image
                  src="/heroImage.png"
                  alt="Blue Oil Container"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
          <div>
            <p className="text-gray-600 text-lg mb-8">
              Premium Lubricants for Automotive, Industrial, Marine & Power
              Applications
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
