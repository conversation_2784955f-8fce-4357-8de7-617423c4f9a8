import { Facebook, Linkedin, Twitter, Youtube } from "lucide-react";
import Link from "next/link";

export default function Footer() {
  const footerLinks = [
    {
      title: "COMPANY",
      links: [
        { name: "About Us", href: "/about" },
        { name: "Leadership", href: "/leadership" },
        { name: "Careers", href: "/careers" },
      ],
    },
    {
      title: "PRODUCTS",
      links: [
        { name: "Automotive", href: "/automotive" },
        { name: "Industrial", href: "/industrial" },
        { name: "Marine", href: "/marine" },
      ],
    },
    {
      title: "SERVICES",
      links: [
        { name: "Distribution", href: "/distribution" },
        { name: "Technical Support", href: "/technical-support" },
      ],
    },
    {
      title: "GOVERNANCE",
      links: [
        { name: "Board", href: "/board" },
        { name: "Policies", href: "/policies" },
      ],
    },
    {
      title: "CONTACT",
      links: [
        { name: "Support", href: "/support" },
        { name: "Locations", href: "/locations" },
      ],
    },
  ];

  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-5 gap-8 mb-8">
          <div>
            <h3 className="font-bold mb-4">COMPANY</h3>
            <ul className="space-y-2 text-gray-400">
              {footerLinks[0].links.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="hover:text-white">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="font-bold mb-4">PRODUCTS</h3>
            <ul className="space-y-2 text-gray-400">
              {footerLinks[1].links.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="hover:text-white">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="font-bold mb-4">SERVICES</h3>
            <ul className="space-y-2 text-gray-400">
              {footerLinks[2].links.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="hover:text-white">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="font-bold mb-4">GOVERNANCE</h3>
            <ul className="space-y-2 text-gray-400">
              {footerLinks[3].links.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="hover:text-white">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="font-bold mb-4">CONTACT</h3>
            <ul className="space-y-2 text-gray-400">
              {footerLinks[4].links.map((link) => (
                <li key={link.name}>
                  <Link href={link.href} className="hover:text-white">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 ASAS Lubricants. All rights reserved.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link href="#" className="text-gray-400 hover:text-white">
              <Facebook className="w-5 h-5" />
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white">
              <Twitter className="w-5 h-5" />
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white">
              <Linkedin className="w-5 h-5" />
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white">
              <Youtube className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
