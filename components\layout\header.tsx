import Link from "next/link";
import { Button } from "../ui/button";
import Logo from "../logo";

export default function Header() {
  const menuItems = [
    { name: "HOME", href: "/" },
    { name: "COMPANY", href: "/company" },
    { name: "PRODUCTS", href: "/products" },
    { name: "SERVICES", href: "/services" },
    { name: "GOVERNANCE", href: "/governance" },
    { name: "CAREERS", href: "/careers" },
    { name: "MEDIA", href: "/media" },
    { name: "CONTACT", href: "/contact" },
  ];

  return (
    <header className="absolute top-0 left-0 right-0 z-50 bg-transparent overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-6 flex items-center justify-between">
        <div className="flex items-center">
          <Logo />
        </div>

        <nav className="hidden md:flex items-center space-x-5">
          {menuItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="text-gray-700 hover:text-blue-600 font-medium text-sm tracking-wide"
            >
              {item.name}
            </Link>
          ))}
        </nav>

        <div className="hidden md:flex items-center space-x-2">
          <Button className="bg-black text-white hover:bg-gray-800 px-6 py-2 text-sm font-medium">
            BE A DISTRIBUTOR
          </Button>
          <Button
            variant="outline"
            className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-2 text-sm font-medium"
          >
            <Link href="/auth/login">
            LOGIN
            </Link>
          </Button>
        </div>
      </div>
    </header>
  );
}
