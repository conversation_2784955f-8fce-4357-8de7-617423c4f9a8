/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Home,
  ShoppingCart,
  BookOpen,
  ChevronDown,
  LogOut,
  Settings,
  User,
} from "lucide-react";
import { motion } from "framer-motion";

import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { signOut, useSession } from "@/lib/auth-client";
import { toast } from "sonner";
import Logo from "../logo";

export default function Navigation() {
  const {
        data: session,
        isPending, //loading state
        error, //error object
        refetch //refetch the session
    } = useSession()
  const pathName = usePathname();
  const router = useRouter();

  // Get role-specific navigation items
  const userRole = (session?.user as any)?.role || "CUSTOMER";

  const getNavItems = () => {
    if (userRole === "ADMIN") {
      return [
        { href: "/admin/dashboard", icon: Home, label: "Admin Dashboard" },
        { href: "/admin/orders", icon: ShoppingCart, label: "Orders Management" },
        { href: "/admin/settings", icon: Settings, label: "Settings" },
        { href: "/blog", icon: BookOpen, label: "Blog" },
      ];
    } else {
      return [
        { href: "/dashboard", icon: Home, label: "Dashboard" },
        // { href: "/orders", icon: ShoppingCart, label: "My Orders" },
        // { href: "/blog", icon: BookOpen, label: "Blog" },
        // { href: "/careers", icon: Briefcase, label: "Careers" },
      ];
    }
  };

  const navItems = getNavItems();

  const handleLogout = async () => {
     await signOut({
      fetchOptions: {
        // onRequest: () => {
        //   setIsPending(true);
        // },
        // onResponse: () => {
        //   setIsPending(false);
        // },
        onError: (ctx) => {
          toast.error(ctx.error.message);
        },
        onSuccess: () => {
          toast.success("Logout successful!");
          router.push("/auth/login");
        },
      },
    });
  };

  return (
    <nav className="bg-black text-white p-4">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-8">
          <div className="flex items-center gap-2">
            <Logo />
          </div>
        </div>

        <div className="flex items-center gap-6">
          {navItems.map(({ href, icon: Icon, label }) => {
            const isActive = pathName === href;
            return (
              <Link href={href} key={href} style={{ textDecoration: "none" }}>
                <motion.div
                  animate={{
                    scale: isActive ? 1.05 : 1,
                    backgroundColor: isActive ? "#1f2937" : "rgba(0,0,0,0)",
                  }}
                  transition={{ type: "spring", stiffness: 200, damping: 18 }}
                  style={{ borderRadius: 100 }}
                  className={
                    isActive
                      ? "backdrop-blur-[3.9px] shadow-[0_0_22px_0_#F2F2F280_inset,0_0_0_1px_#999_inset,-2px_-2px_1px_-2px_#B3B3B3_inset,2px_2px_1px_-2px_#B3B3B3_inset,3px_3px_0.5px_-3.5px_#FFFFFF80_inset]"
                      : ""
                  }
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`text-white rounded-full flex items-center ${
                      isActive ? "bg-transparent text-white" : ""
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {isActive && (
                      <motion.span
                        initial={{ x: 12, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        exit={{ x: 12, opacity: 0 }}
                        transition={{
                          type: "tween",
                          duration: 0.18,
                          ease: "easeOut",
                        }}
                        className="ml-2"
                      >
                        {label}
                      </motion.span>
                    )}
                  </Button>
                </motion.div>
              </Link>
            );
          })}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            {isPending ? (
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-white border-none shadow-none hover:bg-transparent hover:text-white"
              >
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-white text-black">Loading...</AvatarFallback>
                </Avatar>
                <ChevronDown className="w-4 h-4" />
              </Button>
            ) : (


            <Button
              variant="ghost"
              className="flex items-center gap-2 text-white border-none shadow-none hover:bg-transparent hover:text-white"
            >
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-white text-black">{session?.user.name?.slice(0, 2).toLocaleUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="text-left">
                <div className="text-sm font-medium">{session?.user.name}</div>
                <div className="text-xs text-gray-300">{session?.user.role}</div>
              </div>
              <ChevronDown className="w-4 h-4" />
            </Button>
            )
            }
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-black text-white  hover:bg-none cursor-pointer">
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center w-full">
                <User className="w-4 h-4 mr-2" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="flex items-center w-full">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="w-4 h-4 mr-2" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </nav>
  );
}
