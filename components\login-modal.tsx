"use client";

import type React from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";
import { useState } from "react";

interface LoginModalProps {
  children: React.ReactNode;
}

export default function LoginModal({ children }: LoginModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Login attempt:", formData);
    // Handle login logic here
    setIsOpen(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-md bg-white rounded-3xl border-none p-0 overflow-hidden">
        <div className="relative p-8">
          {/* Close Button */}
          <button
            onClick={() => setIsOpen(false)}
            className="absolute right-4 top-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="Close login modal"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>

          {/* Modal Content */}
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-2">
              WELCOME BACK
            </h2>
            <p className="text-gray-600">Please enter your details</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <Input
                type="email"
                placeholder="EMAIL ADDRESS"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="bg-gray-100 border-none rounded-lg py-6 text-center placeholder-gray-500"
                required
                aria-label="Email Address"
              />
            </div>

            <div>
              <Input
                type="password"
                placeholder="PASSWORD"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className="bg-gray-100 border-none rounded-lg py-6 text-center placeholder-gray-500"
                required
                aria-label="Password"
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-black text-white hover:bg-gray-800 py-6 rounded-full text-lg font-medium"
            >
              LOGIN
            </Button>
          </form>

          {/* Forgot Password Link */}
          <div className="text-center mt-6">
            <button className="text-gray-500 hover:text-gray-700 text-sm">
              Forgot your password?
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
