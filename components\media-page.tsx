"use client";

import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Droplets,
  Download,
  Facebook,
  Twitter,
  Linkedin,
  Youtube,
} from "lucide-react";
import { useState } from "react";

export default function MediaPage() {
  const [activeSection, setActiveSection] = useState("events");

  const newsItems = [
    {
      title:
        "ASAD AGREES TO BULK PURCHASE PARTNERSHIP WITH PINNACLE OIL REFINERY",
      image: "/placeholder.svg?height=200&width=400",
      category: "PARTNERSHIP",
      date: "NOV 15, 2024",
    },
    {
      title: "NOTICE OF 36TH ANNUAL GENERAL MEETING",
      image: "/placeholder.svg?height=200&width=400",
      category: "CORPORATE",
      date: "NOV 10, 2024",
    },
    {
      title:
        "OUR DISTRIBUTORS ARE EQUIPPED WITH LUBRICANTS AND PETROLEUM PRODUCTS FOR STRONG PERFORMANCE",
      image: "/placeholder.svg?height=200&width=400",
      category: "DISTRIBUTORS",
      date: "NOV 05, 2024",
    },
  ];

  const detailedNews = [
    {
      title:
        "ASAD AGREES TO BULK PURCHASE PARTNERSHIP WITH PINNACLE OIL REFINERY",
      description:
        "ASAD Lubricants has entered into a strategic bulk purchase agreement with Pinnacle Oil Refinery to enhance our supply chain capabilities and ensure consistent product availability across all distribution channels.",
      image: "/placeholder.svg?height=200&width=300",
      date: "NOV 15, 2024",
      category: "PARTNERSHIP",
    },
    {
      title: "NOTICE OF 36TH ANNUAL GENERAL MEETING",
      description:
        "Shareholders are hereby notified of our 36th Annual General Meeting scheduled to discuss company performance, strategic initiatives, and future growth plans for the upcoming fiscal year.",
      image: "/placeholder.svg?height=200&width=300",
      date: "NOV 10, 2024",
      category: "CORPORATE",
    },
    {
      title:
        "OUR DISTRIBUTORS ARE EQUIPPED WITH LUBRICANTS AND PETROLEUM PRODUCTS FOR STRONG PERFORMANCE",
      description:
        "We continue to strengthen our distributor network by providing comprehensive product portfolios and support systems to ensure optimal market penetration and customer satisfaction.",
      image: "/placeholder.svg?height=200&width=300",
      date: "NOV 05, 2024",
      category: "DISTRIBUTORS",
    },
  ];

  const publications = [
    {
      title: "Annual Report 2018",
      image: "/placeholder.svg?height=300&width=250",
      downloadUrl: "#",
    },
    {
      title: "Sustainability Report 2018",
      image: "/placeholder.svg?height=300&width=250",
      downloadUrl: "#",
    },
    {
      title: "Financial Statement 2018",
      image: "/placeholder.svg?height=300&width=250",
      downloadUrl: "#",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">ASAD</span>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              HOME
            </Link>
            <Link href="/company" className="text-gray-600 hover:text-blue-600">
              COMPANY
            </Link>
            <Link
              href="/products"
              className="text-gray-600 hover:text-blue-600"
            >
              PRODUCTS
            </Link>
            <Link
              href="/services"
              className="text-gray-600 hover:text-blue-600"
            >
              SERVICES
            </Link>
            <Link
              href="/governance"
              className="text-gray-600 hover:text-blue-600"
            >
              GOVERNANCE
            </Link>
            <Link href="/careers" className="text-gray-600 hover:text-blue-600">
              CAREERS
            </Link>
            <Link href="/media" className="text-blue-600 font-medium">
              MEDIA
            </Link>
            <Link href="/contact" className="text-gray-600 hover:text-blue-600">
              CONTACT
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button className="bg-black text-white hover:bg-gray-800">
              BE A DISTRIBUTOR
            </Button>
            <Button variant="outline">LOGIN</Button>
          </div>
        </div>
      </header>

      {/* Media Categories Header */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <div className="inline-flex flex-col space-y-2">
            <span className="bg-blue-500 text-white px-6 py-3 text-xl font-bold">
              EVENTS
            </span>
            <span className="bg-gray-800 text-white px-6 py-3 text-xl font-bold">
              GALLERY
            </span>
            <span className="bg-blue-900 text-white px-6 py-3 text-xl font-bold">
              NEWS RELEASES
            </span>
            <span className="bg-red-600 text-white px-6 py-3 text-xl font-bold">
              PUBLICATIONS
            </span>
          </div>
        </div>
      </section>

      {/* Featured News Cards */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            {newsItems.map((item, index) => (
              <Card
                key={index}
                className="bg-blue-500 text-white overflow-hidden hover:shadow-2xl transition-shadow"
              >
                <div className="h-48">
                  <Image
                    src={item.image || "/placeholder.svg"}
                    alt={item.title}
                    width={400}
                    height={200}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-6">
                  <Badge
                    variant="outline"
                    className="text-blue-100 border-blue-100 mb-3"
                  >
                    {item.category}
                  </Badge>
                  <h3 className="text-lg font-bold mb-3 leading-tight">
                    {item.title}
                  </h3>
                  <p className="text-blue-100 text-sm">{item.date}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">GALLERY</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              A glimpse into our events, meetings, facilities and activities
              that showcase our journey in the energy industry.
            </p>
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 12 }, (_, i) => (
              <div
                key={i}
                className="aspect-square bg-gray-200 rounded-lg overflow-hidden hover:scale-105 transition-transform cursor-pointer"
              >
                <Image
                  src={`/placeholder.svg?height=250&width=250&query=gallery image ${
                    i + 1
                  }`}
                  alt={`Gallery image ${i + 1}`}
                  width={250}
                  height={250}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* News Releases */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              NEWS RELEASES
            </h2>
            <p className="text-gray-600">
              Stay informed on recent company developments and industry updates.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {detailedNews.map((item, index) => (
              <Card
                key={index}
                className="bg-blue-500 text-white overflow-hidden hover:shadow-2xl transition-shadow"
              >
                <div className="h-48">
                  <Image
                    src={item.image || "/placeholder.svg"}
                    alt={item.title}
                    width={300}
                    height={200}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <Badge
                      variant="outline"
                      className="text-blue-100 border-blue-100"
                    >
                      {item.category}
                    </Badge>
                    <span className="text-blue-100 text-xs">{item.date}</span>
                  </div>
                  <h3 className="text-lg font-bold mb-3 leading-tight">
                    {item.title}
                  </h3>
                  <p className="text-blue-100 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Publications */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              PUBLICATIONS
            </h2>
            <p className="text-gray-600">
              Access our latest financial and operational reports.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {publications.map((pub, index) => (
              <Card
                key={index}
                className="bg-white border-none shadow-lg hover:shadow-xl transition-shadow"
              >
                <CardContent className="p-6 text-center">
                  <div className="mb-6">
                    <Image
                      src={pub.image || "/placeholder.svg"}
                      alt={pub.title}
                      width={200}
                      height={280}
                      className="mx-auto rounded-lg shadow-md"
                    />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800 mb-4">
                    {pub.title}
                  </h3>
                  <Button
                    variant="outline"
                    className="w-full bg-transparent"
                    onClick={() => window.open(pub.downloadUrl, "_blank")}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-blue-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">JOIN OUR MAILING LIST</h2>
            <p className="text-blue-200 mb-8">
              Subscribe to our newsletter and unlock a world of exclusive
              benefits. Be the first to know about our latest products, special
              promotions, and exciting updates.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter Your Email"
                className="bg-blue-800 border-blue-700 text-white placeholder-blue-300"
              />
              <Button className="bg-yellow-500 text-black hover:bg-yellow-600">
                SUBSCRIBE NOW
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-6 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-4">COMPANY</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">PRODUCTS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">SERVICES</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">GOVERNANCE</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CAREERS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CONTACT</h3>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2025 All rights reserved</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Terms of Service</span>
              <span className="text-gray-400 text-sm">Privacy Policy</span>
              <div className="flex space-x-4">
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Facebook className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Twitter className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Linkedin className="w-5 h-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white">
                  <Youtube className="w-5 h-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
