"use client";

import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu, Droplets } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false);

  const menuItems = [
    { name: "HOME", href: "/" },
    { name: "COMPANY", href: "/company" },
    { name: "PRODUCTS", href: "/products" },
    { name: "SERVICES", href: "/services" },
    { name: "GOVERNANCE", href: "/governance" },
    { name: "CAREERS", href: "/careers" },
    { name: "MEDIA", href: "/media" },
    { name: "CONTACT", href: "/contact" },
  ];

  return (
    <div className="md:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="p-2">
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 bg-white p-0">
          <div className="flex h-full flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <Droplets className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-800">ASAD</span>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-6 py-8">
              <ul className="space-y-6">
                {menuItems.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-lg font-medium text-gray-700 hover:text-blue-600 block py-2"
                      onClick={() => setIsOpen(false)}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            {/* Footer Buttons */}
            <div className="p-6 border-t space-y-4">
              <Button
                className="w-full bg-black text-white hover:bg-gray-800"
                onClick={() => setIsOpen(false)}
              >
                BE A DISTRIBUTOR
              </Button>
              <Button
                variant="outline"
                className="w-full bg-transparent"
                onClick={() => setIsOpen(false)}
              >
                LOGIN
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
