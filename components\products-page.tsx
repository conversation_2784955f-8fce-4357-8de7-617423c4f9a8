import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Droplets, Facebook, Twitter, Linkedin, Youtube } from "lucide-react";

export default function ProductsPage() {
  const productCategories = [
    "AUTOMOTIVE & INDUSTRIAL ENGINE OILS",
    "AUTOMOTIVE GEAR OIL",
    "HYDRAULIC FLUIDS",
    "COOLANTS",
    "MARINE ENGINE OILS",
    "ELECTRO-MOTIVE DIESEL ENGINE OIL",
    "GAS ENGINE OIL RANGE",
    "AUTOMATIC TRANSMISSION FLUIDS",
    "INDUSTRIAL GEAR OILS",
    "SYSTEM OILS",
    "TURBINE OILS",
    "INDUSTRIAL GREASE",
    "MOULD RELEASE OIL",
  ];

  const products = [
    {
      name: "VISCO 2000 20W/50",
      sizes: "1 LITRE, 4 LITRES, 25 LITRES AND 200 LITRES",
      image: "/placeholder.svg?height=300&width=250",
    },
    {
      name: "TWO-STROKE MOTORCYCLE OIL",
      sizes: "1 LITRE AND 200 LITRES",
      image: "/placeholder.svg?height=300&width=250",
    },
    {
      name: "VISCO 2000 20W/50",
      sizes: "1 LITRE, 4 LITRES, 25 LITRES AND 200 LITRES",
      image: "/placeholder.svg?height=300&width=250",
    },
    {
      name: "TWO-STROKE MOTORCYCLE OIL",
      sizes: "1 LITRE AND 200 LITRES",
      image: "/placeholder.svg?height=300&width=250",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">ASAD</span>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              HOME
            </Link>
            <Link href="/company" className="text-gray-600 hover:text-blue-600">
              COMPANY
            </Link>
            <Link href="/products" className="text-blue-600 font-medium">
              PRODUCTS
            </Link>
            <Link
              href="/services"
              className="text-gray-600 hover:text-blue-600"
            >
              SERVICES
            </Link>
            <Link
              href="/governance"
              className="text-gray-600 hover:text-blue-600"
            >
              GOVERNANCE
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              CAREERS
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              MEDIA
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              CONTACT
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button className="bg-black text-white hover:bg-gray-800">
              BE A DISTRIBUTOR
            </Button>
            <Button variant="outline">LOGIN</Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <ul className="space-y-4">
                {productCategories.map((category, index) => (
                  <li key={index}>
                    <Link
                      href="#"
                      className="text-gray-700 hover:text-blue-600 text-sm font-medium block py-2 border-b border-gray-100 last:border-b-0"
                    >
                      {category}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Products Grid */}
          <div className="lg:col-span-3">
            <div className="grid md:grid-cols-2 gap-8">
              {products.map((product, index) => (
                <Card key={index} className="bg-white overflow-hidden">
                  <div className="bg-gray-100 p-8 flex items-center justify-center h-80">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={250}
                      height={300}
                      className="max-h-full object-contain"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-2">
                      {product.name}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      <span className="font-medium">AVAILABLE SIZES:</span>{" "}
                      {product.sizes}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <section className="bg-blue-900 text-white py-16 mt-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">JOIN OUR MAILING LIST</h2>
            <p className="text-blue-200 mb-8">
              Subscribe to our newsletter and unlock a world of exclusive
              benefits. Be the first to know about our latest products, special
              promotions, and exciting updates.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter Your Email"
                className="bg-white text-gray-800"
              />
              <Button className="bg-gray-800 text-white hover:bg-gray-700">
                SUBSCRIBE NOW
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-6 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-4">COMPANY</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">PRODUCTS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">SERVICES</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">GOVERNANCE</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CAREERS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CONTACT</h3>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2025 All rights reserved</p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-400 hover:text-white">
                <Facebook className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <Twitter className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <Linkedin className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <Youtube className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
