import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Droplets, Facebook, Twitter, Linkedin, Youtube } from "lucide-react";

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">ASAD</span>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              HOME
            </Link>
            <Link href="/company" className="text-gray-600 hover:text-blue-600">
              COMPANY
            </Link>
            <Link
              href="/products"
              className="text-gray-600 hover:text-blue-600"
            >
              PRODUCTS
            </Link>
            <Link href="/services" className="text-blue-600 font-medium">
              SERVICES
            </Link>
            <Link
              href="/governance"
              className="text-gray-600 hover:text-blue-600"
            >
              GOVERNANCE
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              CAREERS
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              MEDIA
            </Link>
            <Link href="#" className="text-gray-600 hover:text-blue-600">
              CONTACT
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button className="bg-black text-white hover:bg-gray-800">
              BE A DISTRIBUTOR
            </Button>
            <Button variant="outline">LOGIN</Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative h-96 bg-gray-100 overflow-hidden">
        <Image
          src="/placeholder.svg?height=400&width=800"
          alt="Workers in blue uniforms"
          width={800}
          height={400}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-4">
            <div className="max-w-lg">
              <h1 className="text-5xl font-bold text-white mb-4">
                BUILT TO
                <br />
                <span className="bg-blue-500 px-4 py-2 inline-block">
                  SUPPORT YOU
                </span>
              </h1>
            </div>
          </div>
        </div>
      </section>

      {/* Description Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-600 text-lg max-w-3xl mx-auto">
            We don&apos;t just supply premium products, we deliver complete
            solutions. Our range of services is designed to help businesses,
            distributors, and industries run smoother, safer, and more
            efficiently.
          </p>
        </div>
      </section>

      {/* Quote Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 max-w-5xl mx-auto leading-tight">
            &quot;WE GO BEYOND PRODUCTS. OUR SERVICES ARE BUILT TO SUPPORT
            PERFORMANCE, RELIABILITY, AND BUSINESS GROWTH.&quot;
          </h2>
        </div>
      </section>

      {/* Services Cards */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-blue-500 text-white rounded-3xl overflow-hidden">
              <CardContent className="p-8">
                <div className="text-blue-200 text-sm mb-4">01</div>
                <h3 className="text-2xl font-bold mb-6">
                  CUSTOM
                  <br />
                  BLENDING
                </h3>
                <p className="text-blue-100">
                  Tailored lubricant formulations to meet your specific
                  industrial or equipment needs.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-blue-500 text-white rounded-3xl overflow-hidden">
              <CardContent className="p-8">
                <div className="text-blue-200 text-sm mb-4">02</div>
                <h3 className="text-2xl font-bold mb-6">
                  BULK SUPPLY &
                  <br />
                  DELIVERY
                </h3>
                <p className="text-blue-100">
                  Reliable nationwide supply of diesel and lubricants — safely
                  delivered where and when you need them.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-blue-500 text-white rounded-3xl overflow-hidden">
              <CardContent className="p-8">
                <div className="text-blue-200 text-sm mb-4">03</div>
                <h3 className="text-2xl font-bold mb-6">
                  TECHNICAL
                  <br />
                  SUPPORT
                </h3>
                <p className="text-blue-100">
                  Expert advice to help you choose the right products and
                  improve equipment efficiency.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-blue-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">JOIN OUR MAILING LIST</h2>
            <p className="text-blue-200 mb-8">
              Subscribe to our newsletter and unlock a world of exclusive
              benefits. Be the first to know about our latest products, special
              promotions, and exciting updates.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter Your Email"
                className="bg-white text-gray-800"
              />
              <Button className="bg-gray-800 text-white hover:bg-gray-700">
                SUBSCRIBE NOW
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-6 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-4">COMPANY</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">PRODUCTS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">SERVICES</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">GOVERNANCE</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CAREERS</h3>
            </div>
            <div>
              <h3 className="font-bold mb-4">CONTACT</h3>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2025 All rights reserved</p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-400 hover:text-white">
                <Facebook className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <Twitter className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <Linkedin className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <Youtube className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
