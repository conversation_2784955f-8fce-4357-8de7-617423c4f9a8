"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  User,
  Shield,
  Mail,
  Calendar,
  Filter,
  EyeIcon,
  EyeOffIcon,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState, useTransition, useCallback, useEffect } from "react";
import { toast } from "sonner";
import { createUser, updateUser, deleteUser } from "@/app/(dashboard)/admin/settings/actions";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

type PrismaUser = {
  id: string;
  name: string | null;
  email: string;
  role: "ADMIN" | "CUSTOMER";
  createdAt: Date;
  lastLoginAt: Date | null;
  emailVerified: boolean;
};

type PaginationData = {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

interface SettingsTableProps {
  users: PrismaUser[];
  pagination: PaginationData;
  searchTerm: string;
  roleFilter: string;
}

export default function SettingsTable({
  users,
  pagination,
  searchTerm: initialSearchTerm,
  roleFilter: initialRoleFilter
}: SettingsTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  // Dialog states
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Form states - Fix: Convert empty string to "all" for role filter
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [roleFilter, setRoleFilter] = useState(initialRoleFilter || "all");
  const [selectedUser, setSelectedUser] = useState<PrismaUser | null>(null);
  const [userToDelete, setUserToDelete] = useState<PrismaUser | null>(null);

  const [isVisible, setIsVisible] = useState(false);
const toggleVisibility = () => setIsVisible((prev) => !prev);

  // Form data
  const [createUserFormData, setCreateUserFormData] = useState({
    name: "",
    email: "",
    password: "",
    role: "CUSTOMER" as "CUSTOMER" | "ADMIN",
  });

  const [editUserFormData, setEditUserFormData] = useState({
    name: "",
    email: "",
    password: "",
    role: "CUSTOMER" as "CUSTOMER" | "ADMIN",
  });

  // Update URL with search params
  const updateSearchParams = useCallback((params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      // Fix: Convert "all" back to empty string for URL params
      const urlValue = value === "all" ? "" : value;
      if (urlValue) {
        newSearchParams.set(key, urlValue);
      } else {
        newSearchParams.delete(key);
      }
    });

    // Reset to page 1 when searching or filtering
    if (params.search !== undefined || params.role !== undefined) {
      newSearchParams.set('page', '1');
    }

    router.push(`${pathname}?${newSearchParams.toString()}`);
  }, [router, pathname, searchParams]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== initialSearchTerm) {
        updateSearchParams({ search: searchTerm, role: roleFilter });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, roleFilter, initialSearchTerm, updateSearchParams]);

  // Handle role filter change
  const handleRoleFilterChange = (value: string) => {
    setRoleFilter(value);
    updateSearchParams({ search: searchTerm, role: value });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateSearchParams({
      search: searchTerm,
      role: roleFilter,
      page: page.toString()
    });
  };

  // Create user handler
  const handleCreateUser = async () => {
    startTransition(async () => {
      try {
        const result = await createUser(createUserFormData);
        if (result.success) {
          toast.success(result.success.reason);
          setIsCreateUserDialogOpen(false);
          setCreateUserFormData({ name: "", email: "", password: "", role: "CUSTOMER" });
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Create user error:", error);
        toast.error("Failed to create user");
      }
    });
  };

  // Edit user handlers
  const openEditUserDialog = (user: PrismaUser) => {
    setSelectedUser(user);
    setEditUserFormData({
      name: user.name || "",
      email: user.email,
      password: "",
      role: user.role,
    });
    setIsEditUserDialogOpen(true);
  };

  const handleEditUser = async () => {
    if (!selectedUser) return;

    startTransition(async () => {
      try {
        const result = await updateUser({
          id: selectedUser.id,
          ...editUserFormData,
        });
        if (result.success) {
          toast.success(result.success.reason);
          setIsEditUserDialogOpen(false);
          setSelectedUser(null);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Update user error:", error);
        toast.error("Failed to update user");
      }
    });
  };

  // Delete user handlers
  const openDeleteDialog = (user: PrismaUser) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    startTransition(async () => {
      try {
        const result = await deleteUser({ id: userToDelete.id });
        if (result.success) {
          toast.success(result.success.reason);
          setIsDeleteDialogOpen(false);
          setUserToDelete(null);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error.reason);
        }
      } catch (error) {
        console.error("Delete user error:", error);
        toast.error("Failed to delete user");
      }
    });
  };

  // Format date helper
  const formatDate = (date: Date | null) => {
    if (!date) return "Never";
    return new Date(date).toLocaleDateString();
  };

  return (
    <div>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>User Management</CardTitle>
          <Dialog
            open={isCreateUserDialogOpen}
            onOpenChange={setIsCreateUserDialogOpen}
          >
            <DialogTrigger asChild>
              <Button disabled={isPending}>
                <Plus className="w-4 h-4 mr-2" />
                Add New User
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New User</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="create-user-name">Full Name</Label>
                  <Input
                    id="create-user-name"
                    value={createUserFormData.name}
                    onChange={(e) =>
                      setCreateUserFormData({
                        ...createUserFormData,
                        name: e.target.value,
                      })
                    }
                    placeholder="Enter full name"
                    disabled={isPending}
                  />
                </div>
               <div className="space-y-2">
                  <Label htmlFor="create-user-email">Email</Label>
                  <Input
                    id="create-user-email"
                    type="email"
                    value={createUserFormData.email}
                    onChange={(e) =>
                      setCreateUserFormData({
                        ...createUserFormData,
                        email: e.target.value,
                      })
                    }
                    placeholder="Enter email address"
                    disabled={isPending}
                  />
                </div>
                 <div className="space-y-2">
                  <Label htmlFor="create-user-password">Password</Label>
                   <div className="relative">

                  
                  <Input
                    id="create-user-password"
                     type={isVisible ? "text" : "password"}
                    value={createUserFormData.password}
                    onChange={(e) =>
                      setCreateUserFormData({
                        ...createUserFormData,
                        password: e.target.value,
                      })
                    }
                    placeholder="Enter password"
                    disabled={isPending}
                  />
                  <button
            className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
            type="button"
            onClick={toggleVisibility}
            aria-label={isVisible ? "Hide password" : "Show password"}
            aria-pressed={isVisible}
            aria-controls="password"
          >
            {isVisible ? (
              <EyeOffIcon size={16} aria-hidden="true" />
            ) : (
              <EyeIcon size={16} aria-hidden="true" />
            )}
          </button>
           </div>
                </div>
                 <div className="space-y-2">
                  <Label htmlFor="create-user-role">Role</Label>
                  <Select
                    value={createUserFormData.role}
                    onValueChange={(value: "CUSTOMER" | "ADMIN") =>
                      setCreateUserFormData({ ...createUserFormData, role: value })
                    }
                    disabled={isPending}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent className="w-10">
                      <SelectItem value="CUSTOMER">Customer</SelectItem>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateUserDialogOpen(false)}
                    disabled={isPending}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateUser} disabled={isPending}>
                    {isPending ? "Creating..." : "Create User"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {/* Search and Filter */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={roleFilter} onValueChange={handleRoleFilterChange}>
              <SelectTrigger className="w-32">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                {/* Fix: Changed empty string to "all" */}
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="ADMIN">Admin</SelectItem>
                <SelectItem value="CUSTOMER">Customer</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    No users found
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span className="font-medium">{user.name || "N/A"}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Mail className="w-4 h-4" />
                        <span>{user.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={user.role === "ADMIN" ? "default" : "secondary"}
                      >
                        <Shield className="w-3 h-3 mr-1" />
                        {user.role}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(user.createdAt)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatDate(user.lastLoginAt)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditUserDialog(user)}
                          disabled={isPending}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openDeleteDialog(user)}
                          disabled={isPending}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.currentPage - 1) * 10) + 1} to{" "}
                {Math.min(pagination.currentPage * 10, pagination.totalCount)} of{" "}
                {pagination.totalCount} users
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious size="sm"
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (pagination.hasPreviousPage) {
                          handlePageChange(pagination.currentPage - 1);
                        }
                      }}
                      className={!pagination.hasPreviousPage ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNumber = Math.max(1, pagination.currentPage - 2) + i;
                    if (pageNumber > pagination.totalPages) return null;

                    return (
                      <PaginationItem key={pageNumber}>
                        <PaginationLink
                          size="sm"
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(pageNumber);
                          }}
                          isActive={pageNumber === pagination.currentPage}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext size="sm"
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (pagination.hasNextPage) {
                          handlePageChange(pagination.currentPage + 1);
                        }
                      }}
                      className={!pagination.hasNextPage ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-user-name">Full Name</Label>
              <Input
                id="edit-user-name"
                value={editUserFormData.name}
                onChange={(e) => setEditUserFormData({ ...editUserFormData, name: e.target.value })}
                placeholder="Enter full name"
                disabled={isPending}
              />
            </div>
             <div className="space-y-2">
              <Label htmlFor="edit-user-email">Email</Label>
              <Input
                id="edit-user-email"
                type="email"
                value={editUserFormData.email}
                onChange={(e) => setEditUserFormData({ ...editUserFormData, email: e.target.value })}
                placeholder="Enter email address"
                disabled={isPending}
              />
            </div>
             <div className="space-y-2">
              <Label htmlFor="edit-user-role">Role</Label>
              <Select
                value={editUserFormData.role}
                onValueChange={(value: "CUSTOMER" | "ADMIN") => setEditUserFormData({ ...editUserFormData, role: value })}
                disabled={isPending}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent className="w-10">
                  <SelectItem value="CUSTOMER">Customer</SelectItem>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsEditUserDialogOpen(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button onClick={handleEditUser} disabled={isPending}>
                {isPending ? "Updating..." : "Update User"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user account for{" "}
              <strong>{userToDelete?.email}</strong> and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteUser}
              disabled={isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isPending ? "Deleting..." : "Delete User"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}