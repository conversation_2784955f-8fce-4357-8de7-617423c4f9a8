import { getUsers } from "@/data/dal";
import SettingsTable from "./settings-table";

interface SettingsWrapperProps {
  page: number;
  search: string;
  role: string;
}

export default async function SettingsWrapper({ page, search, role }: SettingsWrapperProps) {
  const usersData = await getUsers({ page, search, role });
  const users = usersData?.users ?? [];
  const pagination = usersData?.pagination ?? {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  return (
    <SettingsTable 
      users={users} 
      pagination={pagination}
      searchTerm={search}
      roleFilter={role}
    />
  );
}
