import * as React from "react";

export const GoogleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" {...props}>
    <g clipPath="url(#clip0_17_40)">
      <path
        d="M17.64 9.2045C17.64 8.56636 17.5827 7.95272 17.4764 7.36363H9V10.8455H13.8436C13.6364 11.9636 12.9636 12.9181 11.9818 13.5455V15.5545H14.7818C16.3636 14.1272 17.64 11.9272 17.64 9.2045Z"
        fill="#4285F4"
      />
      <path
        d="M9 18C11.43 18 13.4545 17.2091 14.7818 15.5545L11.9818 13.5455C11.2545 14.0455 10.2545 14.3636 9 14.3636C6.65455 14.3636 4.67273 12.8091 3.96364 10.6727H1.06363V12.7545C2.38182 15.2364 5.45455 18 9 18Z"
        fill="#34A853"
      />
      <path
        d="M3.96364 10.6727C3.78182 10.1727 3.67273 9.62727 3.67273 9.04545C3.67273 8.46363 3.78182 7.91818 3.96364 7.41818V5.33636H1.06363C0.545454 6.39091 0.272728 7.67272 0.272728 9.04545C0.272728 10.4182 0.545454 11.7 1.06363 12.7545L3.96364 10.6727Z"
        fill="#FBBC05"
      />
      <path
        d="M9 3.63636C10.3636 3.63636 11.5636 4.10454 12.5091 5.00909L14.8545 2.66363C13.4545 1.34545 11.43 0.272728 9 0.272728C5.45455 0.272728 2.38182 3.03636 1.06363 5.33636L3.96364 7.41818C4.67273 5.28181 6.65455 3.63636 9 3.63636Z"
        fill="#EA4335"
      />
    </g>
    <defs>
      <clipPath id="clip0_17_40">
        <rect width="18" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);