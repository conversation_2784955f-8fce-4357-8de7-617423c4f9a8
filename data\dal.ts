import { prisma } from "@/lib/prisma";
import { getSession } from "./get-session";
import { redirect } from "next/navigation";
import { cache } from "react";

export const getUsers = cache(async ({
  page = 1,
  limit = 10,
  search = "",
  role = "",
}: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
} = {}) => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    return redirect("/auth/login");

  }

  try {
    const skip = (page - 1) * limit;

    // Build where clause
    const where: {
      OR?: Array<{ name: { contains: string; mode: "insensitive" } } | { email: { contains: string; mode: "insensitive" } }>;
      role?: "ADMIN" | "CUSTOMER";
    } = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role && (role === "ADMIN" || role === "CUSTOMER")) {
      where.role = role;
    }

    // Get users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          lastLoginAt: true,
          emailVerified: true,
        },
      }),
      prisma.user.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      users,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return null;
  }
});

// Blog functions
export const getBlogs = cache(async ({
  page = 1,
  limit = 10,
  search = "",
  published = "",
}: {
  page?: number;
  limit?: number;
  search?: string;
  published?: string;
} = {}) => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    return redirect("/auth/login");
  }

  try {
    const skip = (page - 1) * limit;

    // Build where clause
    const where: {
      OR?: Array<{ title: { contains: string; mode: "insensitive" } } | { description: { contains: string; mode: "insensitive" } }>;
      published?: boolean;
    } = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (published === "true") {
      where.published = true;
    } else if (published === "false") {
      where.published = false;
    }

    // Get blogs with pagination
    const [blogs, totalCount] = await Promise.all([
      prisma.blog.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      }),
      prisma.blog.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      blogs,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return null;
  }
});

export const getBlogById = cache(async (id: string) => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    return redirect("/auth/login");
  }

  try {
    const blog = await prisma.blog.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return blog;
  } catch (error) {
    console.error("Error fetching blog:", error);
    return null;
  }
});

export const getBlogStats = cache(async () => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
   return redirect("/auth/login");
  }

  try {
    const [totalBlogs, publishedBlogs, draftBlogs] = await Promise.all([
      prisma.blog.count(),
      prisma.blog.count({ where: { published: true } }),
      prisma.blog.count({ where: { published: false } }),
    ]);

    return {
      totalBlogs,
      publishedBlogs,
      draftBlogs,
    };
  } catch (error) {
    console.error("Error fetching blog stats:", error);
    return null;
  }
});

export const getUserStats = cache(async () => {
    const session = await getSession();
     if(!session) {
       return redirect("/auth/login");
     }

     try {
        const totalUsers = await prisma.user.count();
        const adminUsers = await prisma.user.count({
            where: {
                role: "ADMIN"
            }
        });
        const customerUsers = await prisma.user.count({
            where: {
                role: "CUSTOMER"
            }
        });

        return {
            totalUsers,
            adminUsers,
            customerUsers,
        };
     } catch (error) {
        console.error("Error fetching user stats:", error);
        return null;
     }

});

// Career functions
export const getCareers = cache(async ({
  page = 1,
  limit = 10,
  search = "",
  active = "",
}: {
  page?: number;
  limit?: number;
  search?: string;
  active?: string;
} = {}) => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
   return redirect("/auth/login");
  }

  try {
    const skip = (page - 1) * limit;

    // Build where clause
    const where: {
      OR?: Array<{ title: { contains: string; mode: "insensitive" } } | { location: { contains: string; mode: "insensitive" } }>;
      active?: boolean;
    } = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
      ];
    }

    if (active === "true") {
      where.active = true;
    } else if (active === "false") {
      where.active = false;
    }

    // Get careers with pagination
    const [careers, totalCount] = await Promise.all([
      prisma.career.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          _count: {
            select: {
              applications: true,
            },
          },
        },
      }),
      prisma.career.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      careers,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Error fetching careers:", error);
    return null;
  }
});

export const getCareerById = cache(async (id: string) => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    return redirect("/auth/login");
  }

  try {
    const career = await prisma.career.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    return career;
  } catch (error) {
    console.error("Error fetching career:", error);
    return null;
  }
});

export const getCareerStats = cache(async () => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    return redirect("/auth/login");
  }

  try {
    const [totalCareers, activeCareers, inactiveCareers, totalApplications] = await Promise.all([
      prisma.career.count(),
      prisma.career.count({ where: { active: true } }),
      prisma.career.count({ where: { active: false } }),
      prisma.jobApplication.count(),
    ]);

    return {
      totalCareers,
      activeCareers,
      inactiveCareers,
      totalApplications,
    };
  } catch (error) {
    console.error("Error fetching career stats:", error);
    return null;
  }
});