import { prisma } from "@/lib/prisma";
import { getSession } from "./get-session";
import { unauthorized } from "next/navigation";
import { cache } from "react";

export const getUsers = cache(async ({
  page = 1,
  limit = 10,
  search = "",
  role = "",
}: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
} = {}) => {
  const session = await getSession();
  if (!session || session.user.role !== "ADMIN") {
    return unauthorized();
  }

  try {
    const skip = (page - 1) * limit;

    // Build where clause
    const where: {
      OR?: Array<{ name: { contains: string; mode: "insensitive" } } | { email: { contains: string; mode: "insensitive" } }>;
      role?: "ADMIN" | "CUSTOMER";
    } = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role && (role === "ADMIN" || role === "CUSTOMER")) {
      where.role = role;
    }

    // Get users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          lastLoginAt: true,
          emailVerified: true,
        },
      }),
      prisma.user.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      users,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return null;
  }
});

export const getUserStats = cache(async () => {
    const session = await getSession();
     if(!session) {
        return unauthorized();
     }

     try {
        const totalUsers = await prisma.user.count();
        const adminUsers = await prisma.user.count({
            where: {
                role: "ADMIN"
            }
        });
        const customerUsers = await prisma.user.count({
            where: {
                role: "CUSTOMER"
            }
        });

        return {
            totalUsers,
            adminUsers,
            customerUsers,
        };
     } catch (error) {
        console.error("Error fetching user stats:", error);
        return null;
     }

})