import { prisma } from "@/lib/prisma";
import { getSession } from "./get-session";
import { unauthorized } from "next/navigation";
import { cache } from "react";

export const getUsers = cache(async () => {
const session = await getSession();
 if(!session) {
    return unauthorized();
 }

 try {
      const users = await prisma.user.findMany();
  return users;
 } catch (error) {
    console.error("Error fetching users:", error);
    return null;
 }
  
})

export const getUserStats = cache(async () => {
    const session = await getSession();
     if(!session) {
        return unauthorized();
     }

     try {
        const totalUsers = await prisma.user.count();
        const adminUsers = await prisma.user.count({
            where: {
                role: "ADMIN"
            }
        });
        const customerUsers = await prisma.user.count({
            where: {
                role: "CUSTOMER"
            }
        });

        return {
            totalUsers,
            adminUsers,
            customerUsers,
        };
     } catch (error) {
        console.error("Error fetching user stats:", error);
        return null;
     }
  
})