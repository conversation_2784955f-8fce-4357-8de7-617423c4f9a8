import "server-only"

import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { unauthorized } from "next/navigation";
import { cache } from "react";

export const getSession = cache(async () => {
   const session = await auth.api.getSession({
        headers: await headers()
    })
    if(session?.user.role !== "ADMIN") {
        return unauthorized();
    }
    return session;
    
})