/* eslint-disable @typescript-eslint/no-explicit-any */
import { useSession } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

/**
 * Hook to redirect users to appropriate dashboard based on their role
 */
export function useRoleRedirect() {
  const { data: session } = useSession();
  const router = useRouter();

  const redirectToRoleDashboard = () => {
    if (!session?.user) {
      console.warn('No session available for redirect');
      return;
    }

    const userRole = (session.user as any).role || "CUSTOMER";

    console.log('Redirecting user with role:', userRole);

    switch (userRole) {
      case "ADMIN":
        router.push("/admin/dashboard");
        break;
      case "CUSTOMER":
        router.push("/");
        break;
      default:
        router.push("/");
        break;
    }
  };

  return { redirectToRoleDashboard };
}

/**
 * Hook that automatically redirects authenticated users to their role-based dashboard
 * Useful for auth pages when user is already logged in
 */
export function useAutoRoleRedirect() {
  const { data: session, isPending } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (isPending) return; // Wait for session to load

    if (session?.user) {
      const userRole = (session.user as any).role || "CUSTOMER";

      switch (userRole) {
        case "ADMIN":
          router.replace("/admin/dashboard");
          break;
        case "CUSTOMER":
          router.replace("/");
          break;
        default:
          router.replace("/");
          break;
      }
    }
  }, [session, isPending, router]);

  return { isRedirecting: !!session?.user };
}
