import { createAuthClient } from "better-auth/react";
import { adminClient, inferAdditionalFields } from "better-auth/client/plugins";
import type {auth} from "@/lib/auth";
import { toast } from "sonner";

export const authClient = createAuthClient({
  plugins: [adminClient(), inferAdditionalFields<typeof auth>()],
  fetchOptions: {
    onError: async (context) => {
      const { response } = context;
      if (response.status === 429) {
        const retryAfter = response.headers.get("X-Retry-After");
        const minutes = retryAfter ? Math.ceil(parseInt(retryAfter) / 60) : 5;
        toast.error(
          `Too many requests. Please wait ${minutes} minute${minutes > 1 ? 's' : ''} before trying again.`,
          {
            duration: 5000,
          }
        );
      }
    },
  },
});

export const { signOut,useSession, signIn } = authClient;