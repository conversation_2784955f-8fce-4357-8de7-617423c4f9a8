/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

export interface UserSession {
  user: {
    id: string;
    email: string;
    name?: string | null;
    role: "ADMIN" | "CUSTOMER";
  };
}

/**
 * Get user session from request in middleware
 * This uses Better Auth's server-side session verification
 */
export async function getUserSession(request: NextRequest): Promise<UserSession | null> {
  try {
    // Get the session cookie
    const sessionToken = request.cookies.get("better-auth.session_token")?.value;
    
    if (!sessionToken) {
      return null;
    }

    // Verify session using Better Auth
    const session = await auth.api.getSession({
        headers: await headers()
    });

    if (!session || !session.user) {
      return null;
    }

    return {
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: (session.user as any).role || "CUSTOMER", // Type assertion for role
      },
    };
  } catch (error) {
    console.error("Error getting user session in middleware:", error);
    return null;
  }
}

/**
 * Check if user has required role
 */
export function hasRole(session: UserSession | null, requiredRole: "ADMIN" | "CUSTOMER"): boolean {
  if (!session) return false;
  
  // Admin has access to everything
  if (session.user.role === "ADMIN") return true;
  
  // Check specific role
  return session.user.role === requiredRole;
}

/**
 * Get appropriate redirect URL based on user role
 */
export function getRoleBasedRedirect(role: "ADMIN" | "CUSTOMER"): string {
  switch (role) {
    case "ADMIN":
      return "/admin/dashboard";
    case "CUSTOMER":
      return "/";
    default:
      return "/";
  }
}
