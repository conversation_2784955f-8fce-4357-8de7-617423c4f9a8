import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { prisma } from "@/lib/prisma";
import { nextCookies } from "better-auth/next-js";
import { admin } from "better-auth/plugins/admin";
import { EmailService } from "@/lib/email-service";
import { UserRole } from "@prisma/client";


export const auth = betterAuth({
  baseURL: process.env.BETTER_AUTH_URL || process.env.NEXTAUTH_URL || "http://localhost:3000",
  trustedOrigins: [
    "http://localhost:3000",
    "https://asad-lubricant.vercel.app",
  ],
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      redirectURI: `${process.env.BETTER_AUTH_URL || process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/auth/callback/google`,
    },
  },
  emailAndPassword: {
    enabled: true,
    minPasswordLength: 6,
    autoSignIn: false,
    sendResetPassword: async ({ user, url, token }) => {
      await EmailService.sendPasswordResetEmail({ user, url, token });
    },
    onPasswordReset: async ({ user }) => {
      console.log(`Password for user ${user.email} has been reset successfully.`);
      // Send a confirmation email
      await EmailService.sendPasswordResetConfirmation(user);
    },
  },
  account: {
    accountLinking: {
      enabled: true,
    },
  },
  rateLimit: {
    enabled: true,
    window: 60, // 60 seconds window
    max: 100, // 100 requests per window for general endpoints
    storage: "database", // Store rate limit data in database
    customRules: {
      // Strict rate limiting for forgot password to prevent email spam
      "/request-password-reset": {
        window: 300, // 5 minutes window
        max: 3, // Only 3 forgot password requests per 5 minutes
      },
      // Also limit sign-in attempts
      "/sign-in/email": {
        window: 60, // 1 minute window
        max: 5, // 5 login attempts per minute
      },
      // Limit email verification requests
      "/send-verification-email": {
        window: 300, // 5 minutes window
        max: 3, // Only 3 verification emails per 5 minutes
      },
    },
  },
  user: {
    additionalFields: {
      role: {
        type: ["CUSTOMER", "ADMIN"] as Array<UserRole>,
        input: false,
      },
    },
  },
  advanced: {
    database: {
      generateId: false,
    },
    crossSubDomainCookies: {
      enabled: true,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // Update session every 24 hours
  },
  plugins: [
    nextCookies(),
    admin({
      defaultRole: UserRole.CUSTOMER,
      adminRoles: [UserRole.ADMIN],
    }),
  ],
});
