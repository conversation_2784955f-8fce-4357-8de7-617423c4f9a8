import { sendEmail } from "@/lib/email";
import { passwordResetTemplate, emailVerificationTemplate } from "@/lib/email-templates";

interface User {
  name?: string | null;
  email: string;
}

interface EmailServiceOptions {
  user: User;
  url: string;
  token: string;
}

export class EmailService {
  static async sendPasswordResetEmail({ user, url, token }: EmailServiceOptions) {
    try {
      const template = passwordResetTemplate({ user, url, token });

      await sendEmail({
        to: user.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      console.log(`Password reset email sent to: ${user.email}`);
    } catch (error) {
      console.error(`Failed to send password reset email to ${user.email}:`, error);
      throw new Error('Failed to send password reset email');
    }
  }

  static async sendEmailVerification({ user, url, token }: EmailServiceOptions) {
    try {
      const template = emailVerificationTemplate({ user, url, token });

      await sendEmail({
        to: user.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      console.log(`Email verification sent to: ${user.email}`);
    } catch (error) {
      console.error(`Failed to send email verification to ${user.email}:`, error);
      throw new Error('Failed to send email verification');
    }
  }

  static async sendPasswordResetConfirmation(user: User) {
    try {
      await sendEmail({
        to: user.email,
        subject: "Password Reset Successful",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px 20px;">
            <div style="text-align: center; margin-bottom: 40px;">
              <h1 style="color: #333; font-size: 28px; margin: 0; font-weight: 600;">Password Reset Successful</h1>
            </div>

            <div style="margin-bottom: 30px;">
              <p style="color: #666; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                Hello <strong>${user.name || 'there'}</strong>,
              </p>
              <p style="color: #666; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
                Your password has been successfully reset. You can now log in with your new password.
              </p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
              <p style="color: #666; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong>Security Tip:</strong> If you didn't make this change, please contact our support team immediately.
              </p>
            </div>

            <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #999; font-size: 12px; margin: 0;">
                This email was sent by ${process.env.NODEMAILER_FROM_NAME || 'Your App'}
              </p>
            </div>
          </div>
        `,
        text: `
Password Reset Successful

Hello ${user.name || 'there'},

Your password has been successfully reset. You can now log in with your new password.

Security Tip: If you didn't make this change, please contact our support team immediately.

---
This email was sent by ${process.env.NODEMAILER_FROM_NAME || 'Your App'}
        `.trim(),
      });

      console.log(`Password reset confirmation sent to: ${user.email}`);
    } catch (error) {
      console.error(`Failed to send password reset confirmation to ${user.email}:`, error);
      throw new Error('Failed to send password reset confirmation');
    }
  }

  // You can add more email methods here
  static async sendWelcomeEmail(user: User) {
    try {
      await sendEmail({
        to: user.email,
        subject: "Welcome to our platform!",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Welcome ${user.name || 'there'}!</h2>
            <p>Thank you for joining us. We're excited to have you on board!</p>
          </div>
        `,
        text: `Welcome ${user.name || 'there'}! Thank you for joining us. We're excited to have you on board!`,
      });

      console.log(`Welcome email sent to: ${user.email}`);
    } catch (error) {
      console.error(`Failed to send welcome email to ${user.email}:`, error);
      throw new Error('Failed to send welcome email');
    }
  }
}
