interface User {
  name?: string | null;
  email: string;
}

interface EmailTemplateData {
  user: User;
  url: string;
  token: string;
}

export const passwordResetTemplate = ({ user, url }: EmailTemplateData) => {
  const userName = user.name || 'there';
  
  return {
    subject: "Reset your password",
    html: `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f5f5f5;">
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px 20px;">
          <!-- Header -->
          <div style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #333; font-size: 28px; margin: 0; font-weight: 600;">Reset Your Password</h1>
          </div>
          
          <!-- Main Content -->
          <div style="margin-bottom: 30px;">
            <p style="color: #666; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
              Hello <strong>${userName}</strong>,
            </p>
            <p style="color: #666; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
              We received a request to reset your password. Click the button below to create a new password:
            </p>
          </div>
          
          <!-- CTA Button -->
          <div style="text-align: center; margin: 40px 0;">
            <a href="${url}" 
               style="background-color: #000; color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; display: inline-block; transition: background-color 0.3s ease;">
              Reset Password
            </a>
          </div>
          
          <!-- Security Notice -->
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
            <p style="color: #666; font-size: 14px; line-height: 1.5; margin: 0 0 10px 0;">
              <strong>Security Notice:</strong>
            </p>
            <ul style="color: #666; font-size: 14px; line-height: 1.5; margin: 0; padding-left: 20px;">
              <li>This link will expire in 1 hour for security reasons</li>
              <li>If you didn't request this password reset, you can safely ignore this email</li>
              <li>Your password will remain unchanged until you click the link above</li>
            </ul>
          </div>
          
          <!-- Fallback URL -->
          <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
            <p style="color: #999; font-size: 12px; text-align: center; line-height: 1.4; margin: 0;">
              If you're having trouble clicking the button, copy and paste this URL into your browser:
            </p>
            <p style="color: #666; font-size: 12px; text-align: center; word-break: break-all; margin: 10px 0 0 0;">
              <a href="${url}" style="color: #666;">${url}</a>
            </p>
          </div>
          
          <!-- Footer -->
          <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              This email was sent by ${process.env.NODEMAILER_FROM_NAME || 'Your App'}
            </p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Reset Your Password

Hello ${userName},

We received a request to reset your password. Click the link below to create a new password:

${url}

SECURITY NOTICE:
- This link will expire in 1 hour for security reasons
- If you didn't request this password reset, you can safely ignore this email
- Your password will remain unchanged until you click the link above

If you're having trouble with the link, copy and paste this URL into your browser:
${url}

---
This email was sent by ${process.env.NODEMAILER_FROM_NAME || 'Your App'}
    `.trim(),
  };
};

// You can add more email templates here
export const emailVerificationTemplate = ({ user, url }: EmailTemplateData) => {
  const userName = user.name || 'there';
  
  return {
    subject: "Verify your email address",
    html: `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f5f5f5;">
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px 20px;">
          <!-- Header -->
          <div style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #333; font-size: 28px; margin: 0; font-weight: 600;">Verify Your Email</h1>
          </div>
          
          <!-- Main Content -->
          <div style="margin-bottom: 30px;">
            <p style="color: #666; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
              Hello <strong>${userName}</strong>,
            </p>
            <p style="color: #666; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
              Thank you for signing up! Please verify your email address by clicking the button below:
            </p>
          </div>
          
          <!-- CTA Button -->
          <div style="text-align: center; margin: 40px 0;">
            <a href="${url}" 
               style="background-color: #000; color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; display: inline-block;">
              Verify Email
            </a>
          </div>
          
          <!-- Footer -->
          <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              This email was sent by ${process.env.NODEMAILER_FROM_NAME || 'Your App'}
            </p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Verify Your Email

Hello ${userName},

Thank you for signing up! Please verify your email address by clicking the link below:

${url}

If you're having trouble with the link, copy and paste this URL into your browser:
${url}

---
This email was sent by ${process.env.NODEMAILER_FROM_NAME || 'Your App'}
    `.trim(),
  };
};
