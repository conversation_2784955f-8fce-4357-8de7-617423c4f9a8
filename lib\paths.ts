// Define public paths that don't require authentication
export function isPublicPath(pathname: string): boolean {
  const publicPaths = [
    "/",
    "/auth/login",
    "/auth/register",
    "/auth/reset-password",
    "/auth/forgot-password",
    "/company",
    "/products",
    "/services",
    "/governance",
    "/careers",
    "/contact",
    "/media",
    "/api/auth",
  ];

  return publicPaths.some(path =>
    pathname === path || pathname.startsWith(path + "/")
  );
}

// Role-based redirect paths
export const ROLE_REDIRECTS = {
  ADMIN: "/admin/dashboard",
  CUSTOMER: "/",
} as const;

// Admin-only paths that require ADMIN role
export function isAdminPath(pathname: string): boolean {
  return pathname.startsWith("/admin");
}

// Customer dashboard paths
export function isCustomerPath(pathname: string): boolean {
  const customerPaths = [
    "/dashboard",
    "/orders",
    "/blog",
    "/careers"
  ];

  return customerPaths.some(path =>
    pathname === path || pathname.startsWith(path + "/")
  );
}

// Default redirects (keeping backward compatibility)
const DEFAULT_LOGIN_REDIRECT = "/";

export { DEFAULT_LOGIN_REDIRECT };
