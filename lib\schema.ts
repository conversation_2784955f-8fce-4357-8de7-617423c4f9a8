import { z } from "zod";


export type ActionResult<T = unknown> = {
  success: { reason: string } | null;
  error: { reason: string } | null;
  data?: T;
};


export const LoginSchema = z.object({
  email: z.string().email({ message: "Invalid email" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

export const RegisterSchema = z.object({
  name: z.string().min(3, { message: "Name must be at least 3 characters" }),
  email: z.string().email({ message: "Invalid email" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

export const ForgotPasswordSchema = z.object({
  email: z.string().email({ message: "Invalid email" }),
});

export const CreateUserSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  role: z.enum(["CUSTOMER", "ADMIN"], { message: "Invalid role" }),
});

export const UpdateUserSchema = z.object({
  id: z.string().min(1, { message: "User ID is required" }),
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email" }),
  password: z.string().optional(), // Optional for updates
  role: z.enum(["CUSTOMER", "ADMIN"], { message: "Invalid role" }),
});

export const DeleteUserSchema = z.object({
  id: z.string().min(1, { message: "User ID is required" }),
});

// Blog schemas

export const CreateBlogSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }).max(200, { message: "Title must be less than 200 characters" }),
  description: z.string().min(1, { message: "Description is required" }).max(500, { message: "Description must be less than 500 characters" }),
  content: z.string().min(1, { message: "Content is required" }),
  image: z.string().optional(),
  published: z.boolean().optional() // This makes it optional in input, with default
});

export const UpdateBlogSchema = z.object({
  id: z.string().min(1, { message: "Blog ID is required" }),
  title: z.string().min(1, { message: "Title is required" }).max(200, { message: "Title must be less than 200 characters" }),
  description: z.string().min(1, { message: "Description is required" }).max(500, { message: "Description must be less than 500 characters" }),
  content: z.string().min(1, { message: "Content is required" }),
  image: z.string().optional(),
  published: z.boolean().default(false),
});

export const DeleteBlogSchema = z.object({
  id: z.string().min(1, { message: "Blog ID is required" }),
});

export const ResetPasswordSchema = z.object({
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
  confirmPassword: z.string().min(8, { message: "Password must be at least 8 characters" }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});


export type LoginFormData = z.infer<typeof LoginSchema>;
export type RegisterFormData = z.infer<typeof RegisterSchema>;
export type ForgotPasswordFormData = z.infer<typeof ForgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof ResetPasswordSchema>;
export type CreateUserFormData = z.infer<typeof CreateUserSchema>;
export type UpdateUserFormData = z.infer<typeof UpdateUserSchema>;
export type DeleteUserFormData = z.infer<typeof DeleteUserSchema>;
export type CreateBlogFormData = z.infer<typeof CreateBlogSchema>;
export type UpdateBlogFormData = z.infer<typeof UpdateBlogSchema>;
export type DeleteBlogFormData = z.infer<typeof DeleteBlogSchema>;