import { isPublicPath } from "@/lib/paths";
import { getUserSession, getRoleBasedRedirect } from "@/lib/auth-middleware";
import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get user session
  const session = await getUserSession(request);

  // If user is authenticated and trying to access auth pages, redirect based on role
  if (session && pathname.startsWith("/auth/")) {
    const redirectUrl = getRoleBasedRedirect(session.user.role);
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // Protect admin routes - require ADMIN role
  if (pathname.startsWith("/admin/")) {
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  // Allow access to public paths
  if (isPublicPath(pathname)) {
    return NextResponse.next();
  }

  // For protected paths, require authentication
  if (!session) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  return NextResponse.next();
}
// Match all routes except for static files and Next.js internal routes
export const config = {
  runtime: "nodejs",
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};