{"name": "asad-lubricant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma migrate deploy && prisma generate && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.859.0", "@hookform/resolvers": "^5.2.0", "@prisma/client": "6.5.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-character-count": "^3.0.9", "@tiptap/extension-color": "^3.0.9", "@tiptap/extension-highlight": "^3.0.9", "@tiptap/extension-image": "^3.0.9", "@tiptap/extension-link": "^3.0.9", "@tiptap/extension-text-align": "^3.0.9", "@tiptap/extension-text-style": "^3.0.9", "@tiptap/extension-underline": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "better-auth": "^1.3.1", "better-upload": "^1.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "^15.4.2-canary.30", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "recharts": "3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@21st-extension/toolbar": "^0.5.14", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "prisma": "6.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}