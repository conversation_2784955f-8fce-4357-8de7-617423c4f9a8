// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model with Better Auth integration and role-based access
model User {
  id            String   @id @default(cuid())
  name          String?
  email         String   @unique
  emailVerified Boolean  @default(false)
  image         String?
  role          UserRole @default(CUSTOMER)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt


  // Security fields
  lastLoginAt   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?
  banned        Boolean?
  bannedReason  String?
  banExpires  DateTime?

  // Better Auth relations
  sessions Session[]
  accounts Account[]

  // E-commerce relations
  orders          Order[]
  jobApplications JobApplication[]
  blogs           Blog[]

  @@map("users")
}

enum UserRole {
  CUSTOMER
  ADMIN
}

// Better Auth Session model
model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  impersonatedBy String?

  @@map("sessions")
}

// Better Auth Account model
model Account {
  id                    String    @id @default(cuid())
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@map("accounts")
}

// Better Auth Verification model
model Verification {
  id         String    @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime? @default(now())
  updatedAt  DateTime? @updatedAt

  @@map("verifications")
}

// Product model with inventory tracking
model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  price       Decimal  @db.Decimal(10, 2)
  image       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  productTypes ProductType[]
  orderItems   OrderItem[]

  @@map("products")
}

// Product types (e.g., T-shirt, Hoodie, etc.)
model ProductType {
  id        String   @id @default(cuid())
  name      String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  product    Product       @relation(fields: [productId], references: [id], onDelete: Cascade)
  sizes      ProductSize[]
  orderItems OrderItem[]

  @@map("product_types")
}

// Product sizes with inventory
model ProductSize {
  id            String   @id @default(cuid())
  size          String // S, M, L, XL, etc.
  inventory     Int      @default(0)
  productTypeId String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  productType ProductType @relation(fields: [productTypeId], references: [id], onDelete: Cascade)
  orderItems  OrderItem[]

  @@unique([productTypeId, size])
  @@map("product_sizes")
}

// Order model with status tracking
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique @default(cuid())
  userId      String
  status      OrderStatus @default(SUBMITTED)
  totalAmount Decimal     @db.Decimal(10, 2)
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user       User        @relation(fields: [userId], references: [id])
  orderItems OrderItem[]

  @@map("orders")
}

enum OrderStatus {
  SUBMITTED
  CONFIRMED
  PAID
  DELIVERED
  CANCELLED
}

// Order items linking orders to products
model OrderItem {
  id            String   @id @default(cuid())
  orderId       String
  productId     String
  productTypeId String
  productSizeId String
  quantity      Int
  unitPrice     Decimal  @db.Decimal(10, 2)
  totalPrice    Decimal  @db.Decimal(10, 2)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  order       Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id])
  productType ProductType @relation(fields: [productTypeId], references: [id])
  productSize ProductSize @relation(fields: [productSizeId], references: [id])

  @@map("order_items")
}

// Blog model for content management
model Blog {
  id          String   @id @default(cuid())
  title       String
  description String
  content     String?  @db.Text
  image       String?
  published   Boolean  @default(false)
  authorId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  author User @relation(fields: [authorId], references: [id])

  @@map("blogs")
}

// Career/Job postings model
model Career {
  id           String   @id @default(cuid())
  title        String
  description  String   @db.Text
  requirements String   @db.Text
  location     String?
  type         String? // Full-time, Part-time, Contract, etc.
  salary       String?
  active       Boolean  @default(true)
  postedDate   DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  applications JobApplication[]

  @@map("careers")
}

// Job applications model
model JobApplication {
  id          String            @id @default(cuid())
  careerId    String
  applicantId String
  firstName   String
  lastName    String
  email       String
  phone       String?
  resume      String? // File path or URL
  coverLetter String?           @db.Text
  status      ApplicationStatus @default(PENDING)
  appliedAt   DateTime          @default(now())
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  career    Career @relation(fields: [careerId], references: [id], onDelete: Cascade)
  applicant User   @relation(fields: [applicantId], references: [id])

  @@unique([careerId, applicantId])
  @@map("job_applications")
}

enum ApplicationStatus {
  PENDING
  REVIEWING
  INTERVIEWED
  ACCEPTED
  REJECTED
}

// Better Auth Rate Limit model
model RateLimit {
  id          String @id @default(cuid())
  key         String
  count       Int
  lastRequest BigInt

  @@map("rateLimit")
}
