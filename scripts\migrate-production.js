#!/usr/bin/env node

/**
 * Production Migration Script
 * 
 * This script helps you manually run database migrations on production.
 * 
 * Usage:
 * 1. Get your production DATABASE_URL from Vercel
 * 2. Run: DATABASE_URL="your-production-url" node scripts/migrate-production.js
 */

const { execSync } = require('child_process');

console.log('🚀 Starting production database migration...');

try {
  // Check if DATABASE_URL is provided
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is required');
    console.log('💡 Usage: DATABASE_URL="your-production-url" node scripts/migrate-production.js');
    process.exit(1);
  }

  console.log('📊 Running Prisma migration deploy...');
  
  // Run the migration
  execSync('npx prisma migrate deploy', {
    stdio: 'inherit',
    env: { ...process.env }
  });

  console.log('✅ Migration completed successfully!');
  console.log('🎉 Your production database is now up to date.');

} catch (error) {
  console.error('❌ Migration failed:', error.message);
  console.log('💡 Please check your DATABASE_URL and try again.');
  process.exit(1);
}
